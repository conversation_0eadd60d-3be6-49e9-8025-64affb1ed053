<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="referrerPath" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ organization?.name || '組織' }}</ion-title>
        <ion-buttons slot="end" v-if="isOwner">
          <ion-button @click="showEditModal = true">
            <ion-icon :icon="createOutline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="organization" class="organization-container">
        <!-- Organization Banner -->
        <div class="organization-banner">
          <img :src="organization.banner || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952'" :alt="organization.name">
          <div class="organization-info-overlay">
            <div class="organization-logo">
              <img :src="organization.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'" :alt="organization.name">
            </div>
            <div class="organization-details">
              <h1>{{ organization.name }}</h1>
              <p class="organization-description clamp-text">{{ organization.description }}</p>
              <div class="organization-meta">
                <div class="meta-item">
                  <ion-icon :icon="peopleOutline"></ion-icon>
                  <span>{{ organization.member_count || 0 }} 個分會</span>
                </div>
                <div class="meta-item">
                  <ion-icon :icon="timeOutline"></ion-icon>
                  <span>{{ formatDate(organization.created_at) }} 創建</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Organization Owner Information -->
        <div class="organization-owner-section">
          <ion-item lines="none" class="organization-owner-item">
            <ion-icon :icon="personOutline" slot="start" class="owner-icon"></ion-icon>
            <ion-label>
              <h3>組織管理員</h3>
              <h2>{{ organizationOwnerName }}</h2>
            </ion-label>
          </ion-item>
        </div>

        <div v-if="!isOwner && isBranchOwner && !isMember && !hasApplied" class="join-section">
          <ion-button expand="block" @click="handleJoinButtonClick">
            申請加入組織
            <ion-icon :icon="addOutline" slot="end"></ion-icon>
          </ion-button>
        </div>
        <div v-else-if="hasApplied" class="join-section">
          <ion-note color="medium">您的申請正在審核中</ion-note>
        </div>

        <!-- Member Branches List -->
        <div class="section">
          <h2>組織分會 ({{ memberBranches.length }})</h2>

          <!-- Pending Applications Section (Only visible to organization owner) -->
          <div v-if="isOwner && pendingApplications.length > 0" class="section applications-section">
            <h2>待審核申請 ({{ pendingApplications.length }})</h2>
            <div class="applications-list">
              <ion-card v-for="application in pendingApplications" :key="application.id" class="application-card">
                <ion-card-header>
                  <ion-card-title>{{ application.branches?.name }}</ion-card-title>
                  <ion-card-subtitle>{{ formatDate(application.created_at) }} 申請</ion-card-subtitle>
                </ion-card-header>
                <ion-card-content>
                  <p v-if="application.message">{{ application.message }}</p>
                  <p v-else class="empty-text">無申請訊息</p>
                  <div class="application-actions">
                    <ion-button color="success" @click="showApproveConfirm(application)">
                      <ion-icon :icon="checkmarkOutline" slot="start"></ion-icon>
                      批准
                    </ion-button>
                    <ion-button color="danger" fill="outline" @click="showRejectConfirm(application)">
                      <ion-icon :icon="closeOutline" slot="start"></ion-icon>
                      拒絕
                    </ion-button>
                  </div>
                </ion-card-content>
              </ion-card>
            </div>
          </div>

          <!-- Active Member Branches -->
          <div v-if="memberBranches.length > 0" class="branches-container">
            <div class="branches-grid">
              <ion-card
                v-for="branch in displayedBranches"
                :key="branch.branch_id"
                class="branch-card compact"
              >
                <div class="compact-branch-content" @click="$router.push(`/branches/${branch.branch_id}`)">
                  <div class="compact-branch-logo">
                    <img :src="branch.branches.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'" :alt="branch.branches.name">
                  </div>
                  <div class="compact-branch-info">
                    <h3>{{ branch.branches.name }}</h3>
                    <p>{{ formatCompactDate(branch.joined_at) }}</p>
                  </div>
                </div>
                <div v-if="isOwner && !isOwnerBranch(branch)" class="branch-actions">
                  <ion-button fill="clear" color="danger" size="small" title="移除分會" @click.stop="confirmKickBranch(branch)">
                    <ion-icon :icon="personRemoveOutline" slot="icon-only"></ion-icon>
                  </ion-button>
                </div>
              </ion-card>
            </div>

            <!-- View All Branches Button (only if there are more branches than shown) -->
            <div v-if="memberBranches.length > maxDisplayedBranches" class="view-all-container">
              <ion-button expand="block" fill="clear" @click="showBranchesModal = true">
                查看全部 {{ memberBranches.length }} 個分會
                <ion-icon :icon="peopleOutline" slot="end"></ion-icon>
              </ion-button>
            </div>
          </div>
          <p v-else class="empty-text">暫無組織分會</p>
        </div>

        <!-- Organization Content -->
        <div class="organization-content">
          <ion-segment v-model="activeTab" mode="md" scrollable>
            <ion-segment-button value="intro">
              <ion-label>組織介紹</ion-label>
            </ion-segment-button>
            <ion-segment-button value="philosophy">
              <ion-label>核心理念</ion-label>
            </ion-segment-button>
            <ion-segment-button value="activities">
              <ion-label>相關活動</ion-label>
            </ion-segment-button>
            <ion-segment-button value="shops">
              <ion-label>相關商店</ion-label>
            </ion-segment-button>
          </ion-segment>

          <!-- Introduction Tab -->
          <div v-if="activeTab === 'intro'" class="tab-content">
            <div class="section">
              <DescriptionSection sectionTitle="" :description="organization.introduction" :truncateLength="500"></DescriptionSection>
            </div>
          </div>

          <!-- Philosophy Tab -->
          <div v-if="activeTab === 'philosophy'" class="tab-content">
            <div class="section">
              <DescriptionSection sectionTitle="" :description="organization.philosophy" :truncateLength="500"></DescriptionSection>
            </div>
          </div>

          <!-- Activities Tab -->
          <div v-if="activeTab === 'activities'" class="tab-content">
            <div class="section">
              <h2>即將舉行的活動</h2>
              <div v-if="upcomingEvents.length > 0" class="events-grid">
                <EventCard v-for="event in upcomingEvents" :key="event.id" :event="event" />
              </div>
              <p v-else class="empty-text">暫無即將舉行的活動</p>

              <div v-if="upcomingEvents.length > maxDisplayedEvents" class="view-all-container">
                <ion-button expand="block" fill="clear" @click="showEventsModal = true">
                  查看全部 {{ upcomingEvents.length }} 個活動
                  <ion-icon :icon="calendarOutline" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <div class="section">
              <h2>已結束的活動</h2>
              <div v-if="pastEvents.length > 0" class="events-grid">
                <EventCard v-for="event in pastEvents.slice(0, 3)" :key="event.id" :event="event" />
              </div>
              <p v-else class="empty-text">暫無已結束的活動</p>

              <div v-if="pastEvents.length > 3" class="view-all-container">
                <ion-button expand="block" fill="clear" @click="showPastEventsModal = true">
                  查看全部 {{ pastEvents.length }} 個已結束活動
                  <ion-icon :icon="calendarOutline" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Shops Tab -->
          <div v-if="activeTab === 'shops'" class="tab-content">
            <div class="section">
              <h2>相關商店</h2>

              <!-- Shop Category Filter Tabs -->
              <div v-if="relatedShops.length > 0" class="shop-category-filter">
                <ion-segment v-model="selectedShopCategory" mode="ios" scrollable>
                  <ion-segment-button value="all">
                    <ion-label>全部 ({{ relatedShops.length }})</ion-label>
                  </ion-segment-button>
                  <ion-segment-button
                    v-for="category in shopCategories"
                    :key="category.id"
                    :value="category.id"
                  >
                    <ion-label>{{ category.title }} ({{ getShopCountByCategory(category.id) }})</ion-label>
                  </ion-segment-button>
                </ion-segment>
              </div>

              <div v-if="filteredShops.length > 0" class="shops-grid">
                <ShopCard
                  v-for="shop in displayedFilteredShops"
                  :key="shop.id"
                  :shop="shop"
                />
              </div>
              <p v-else-if="relatedShops.length > 0" class="empty-text">此分類暫無商店</p>
              <p v-else class="empty-text">暫無相關商店</p>

              <div v-if="filteredShops.length > maxDisplayedShops" class="view-all-container">
                <ion-button expand="block" fill="clear" @click="showShopsModal = true">
                  查看全部 {{ filteredShops.length }} 個商店
                  <ion-icon :icon="storefrontOutline" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline"></ion-icon>
        <p>找不到組織資料</p>
        <ion-button router-link="/organizations" fill="outline">
          返回組織列表
        </ion-button>
      </div>

      <!-- Organization Edit Modal -->
      <OrganizationFormModal
        :is-open="showEditModal"
        :organization="organization"
        @close="showEditModal = false"
        @updated="handleOrganizationUpdated"
      />

      <!-- Join Organization Modal -->
      <ion-modal :is-open="showJoinModal" @didDismiss="showJoinModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>申請加入組織</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showJoinModal = false">取消</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <form @submit.prevent="submitJoinApplication">
            <ion-item>
              <ion-label position="stacked">選擇分會</ion-label>
              <ion-select v-model="selectedBranchId" placeholder="選擇要加入組織的分會">
                <ion-select-option v-for="branch in userOwnedBranches" :key="branch.id" :value="branch.id">
                  {{ branch.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
            <ion-item>
              <ion-label position="stacked">申請訊息</ion-label>
              <ion-textarea
                v-model="applicationMessage"
                placeholder="請簡單介紹分會，並說明為何想加入此組織"
                :rows="6"
              ></ion-textarea>
            </ion-item>
            <div class="ion-padding">
              <ion-button
                type="submit"
                expand="block"
                :disabled="isSubmittingApplication || !selectedBranchId"
              >
                <ion-spinner v-if="isSubmittingApplication" name="crescent"></ion-spinner>
                <span v-else>提交申請</span>
              </ion-button>
            </div>
          </form>
        </ion-content>
      </ion-modal>

      <!-- Approve Confirmation Alert -->
      <ion-alert
        :is-open="showApproveAlertFlag"
        header="確認批准"
        message="確定要批准此申請嗎？批准後，該分會將成為組織成員。"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { showApproveAlertFlag = false; }
          },
          {
            text: '確定批准',
            handler: () => {
              approveApplication(currentApplication);
              showApproveAlertFlag = false;
            }
          }
        ]"
      ></ion-alert>

      <!-- Reject Confirmation Alert -->
      <ion-alert
        :is-open="showRejectAlertFlag"
        header="確認拒絕"
        message="確定要拒絕此申請嗎？"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { showRejectAlertFlag = false; }
          },
          {
            text: '確定拒絕',
            handler: () => {
              rejectApplication(currentApplication);
              showRejectAlertFlag = false;
            }
          }
        ]"
      ></ion-alert>

      <!-- Branches Modal -->
      <ion-modal :is-open="showBranchesModal" @didDismiss="showBranchesModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>組織分會 ({{ memberBranches.length }})</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showBranchesModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="branches-grid-modal">
            <ion-card
              v-for="branch in memberBranches"
              :key="branch.branch_id"
              class="branch-card compact"
            >
              <div class="compact-branch-content" @click="$router.push(`/branches/${branch.branch_id}`); showBranchesModal = false">
                <div class="compact-branch-logo">
                  <img :src="branch.branches.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'" :alt="branch.branches.name">
                </div>
                <div class="compact-branch-info">
                  <h3>{{ branch.branches.name }}</h3>
                  <p>{{ formatCompactDate(branch.joined_at) }}</p>
                </div>
              </div>
              <div v-if="isOwner && !isOwnerBranch(branch)" class="branch-actions">
                <ion-button fill="clear" color="danger" size="small" title="移除分會" @click.stop="confirmKickBranch(branch)">
                  <ion-icon :icon="personRemoveOutline" slot="icon-only"></ion-icon>
                </ion-button>
              </div>
            </ion-card>
          </div>
        </ion-content>
      </ion-modal>

      <!-- Upcoming Events Modal -->
      <ion-modal :is-open="showEventsModal" @didDismiss="showEventsModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>即將舉行的活動 ({{ upcomingEvents.length }})</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showEventsModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="events-grid-modal">
            <EventCard
              v-for="event in upcomingEvents"
              :key="event.id"
              :event="event"
              @click="showEventsModal = false"
            />
          </div>
        </ion-content>
      </ion-modal>

      <!-- Past Events Modal -->
      <ion-modal :is-open="showPastEventsModal" @didDismiss="showPastEventsModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>已結束的活動 ({{ pastEvents.length }})</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showPastEventsModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="events-grid-modal">
            <EventCard
              v-for="event in pastEvents"
              :key="event.id"
              :event="event"
              @click="showPastEventsModal = false"
            />
          </div>
        </ion-content>
      </ion-modal>

      <!-- Shops Modal -->
      <ion-modal :is-open="showShopsModal" @didDismiss="showShopsModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>相關商店 ({{ filteredShops.length }})</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showShopsModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <!-- Shop Category Filter Tabs in Modal -->
          <div v-if="relatedShops.length > 0" class="shop-category-filter modal-filter">
            <ion-segment v-model="selectedShopCategory" mode="ios" scrollable>
              <ion-segment-button value="all">
                <ion-label>全部 ({{ relatedShops.length }})</ion-label>
              </ion-segment-button>
              <ion-segment-button
                v-for="category in shopCategories"
                :key="category.id"
                :value="category.id"
              >
                <ion-label>{{ category.title }} ({{ getShopCountByCategory(category.id) }})</ion-label>
              </ion-segment-button>
            </ion-segment>
          </div>

          <div class="shops-grid-modal">
            <ShopCard
              v-for="shop in filteredShops"
              :key="shop.id"
              :shop="shop"
              @click="showShopsModal = false"
            />
          </div>
        </ion-content>
      </ion-modal>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
  IonAlert,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonItem,
  IonNote,
  IonModal,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  onIonViewDidEnter,
  alertController,
} from '@ionic/vue';
import {
  createOutline,
  alertCircleOutline,
  peopleOutline,
  timeOutline,
  addOutline,
  personOutline,
  checkmarkOutline,
  closeOutline,
  calendarOutline,
  storefrontOutline,
  personRemoveOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useOrganizationStore } from '@/stores/organization';
import OrganizationFormModal from '@/components/OrganizationFormModal.vue';
import DescriptionSection from '@/components/DescriptionSection.vue';
import EventCard from '@/components/EventCard.vue';
import ShopCard from '@/components/ShopCard.vue';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const organizationStore = useOrganizationStore();
const organizationId = route.params.organizationId as string;

// Track the referrer path for back navigation
const referrerPath = ref('/organizations');

const organization = ref<any>(null);
const memberBranches = ref<any[]>([]);
const pendingApplications = ref<any[]>([]);
const userOwnedBranches = ref<any[]>([]);
const relatedEvents = ref<any[]>([]);
const relatedShops = ref<any[]>([]);
const isLoading = ref(true);
const showEditModal = ref(false);
const showJoinModal = ref(false);
const toastMessage = ref('');
const activeTab = ref('intro');
const applicationMessage = ref('');
const selectedBranchId = ref('');
const isSubmittingApplication = ref(false);
const hasApplied = ref(false);
const isMember = ref(false);
const isProcessingApplication = ref(false);
const showApproveAlertFlag = ref(false);
const showRejectAlertFlag = ref(false);
const currentApplication = ref<any>(null);
const showBranchesModal = ref(false);
const showEventsModal = ref(false);
const showPastEventsModal = ref(false);
const showShopsModal = ref(false);
const maxDisplayedBranches = ref(6); // Show 6 branches by default
const maxDisplayedEvents = ref(6); // Show 6 events by default
const maxDisplayedShops = ref(6); // Show 6 shops by default
const selectedShopCategory = ref('all'); // Selected shop category filter

const isOwner = computed(() => {
  return organization.value?.owner_id === authStore.currentUser?.id;
});

const isBranchOwner = computed(() => {
  return authStore.isAuthenticated && userStore.currentUser?.role === 'president';
});

const organizationOwnerName = computed(() => {
  return organization.value?.owner?.full_name || '未知';
});

// Compute the branches to display in the compact view
const displayedBranches = computed(() => {
  // If there are fewer branches than the max, show all
  if (memberBranches.value.length <= maxDisplayedBranches.value) {
    return memberBranches.value;
  }

  // Otherwise, show only the first few branches
  return memberBranches.value.slice(0, maxDisplayedBranches.value);
});

// Compute the events to display in the compact view
const displayedEvents = computed(() => {
  // If there are fewer events than the max, show all
  if (relatedEvents.value.length <= maxDisplayedEvents.value) {
    return relatedEvents.value;
  }

  // Otherwise, show only the first few events
  return relatedEvents.value.slice(0, maxDisplayedEvents.value);
});

// Compute the shops to display in the compact view
const displayedShops = computed(() => {
  // If there are fewer shops than the max, show all
  if (relatedShops.value.length <= maxDisplayedShops.value) {
    return relatedShops.value;
  }

  // Otherwise, show only the first few shops
  return relatedShops.value.slice(0, maxDisplayedShops.value);
});

// Compute upcoming and past events
const upcomingEvents = computed(() => {
  const now = new Date();
  return relatedEvents.value
    .filter(event => new Date(event.start_datetime) >= now)
    .sort((a, b) => new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime());
});

const pastEvents = computed(() => {
  const now = new Date();
  return relatedEvents.value
    .filter(event => new Date(event.start_datetime) < now)
    .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime());
});

// Compute unique shop categories from related shops
const shopCategories = computed(() => {
  const categories = new Map();

  relatedShops.value.forEach(shop => {
    if (shop.shop_categories) {
      categories.set(shop.shop_categories.id, shop.shop_categories);
    }
  });

  return Array.from(categories.values()).sort((a, b) => a.title.localeCompare(b.title));
});

// Filter shops by selected category
const filteredShops = computed(() => {
  if (selectedShopCategory.value === 'all') {
    return relatedShops.value;
  }

  return relatedShops.value.filter(shop =>
    shop.shop_categories?.id === selectedShopCategory.value
  );
});

// Compute the filtered shops to display in the compact view
const displayedFilteredShops = computed(() => {
  // If there are fewer shops than the max, show all
  if (filteredShops.value.length <= maxDisplayedShops.value) {
    return filteredShops.value;
  }

  // Otherwise, show only the first few shops
  return filteredShops.value.slice(0, maxDisplayedShops.value);
});

// Get shop count by category
const getShopCountByCategory = (categoryId: string) => {
  return relatedShops.value.filter(shop =>
    shop.shop_categories?.id === categoryId
  ).length;
};

const loadOrganizationDetails = async () => {
  try {
    const data = await organizationStore.getOrganizationById(organizationId);
    organization.value = data;

    if (data.organization_branches) {
      console.log('Raw organization_branches data:', data.organization_branches);
      console.log('Active branches count:', data.organization_branches.length);
      // The store already filters out inactive branches, so we can use the data directly
      memberBranches.value = data.organization_branches;
      console.log('Final memberBranches:', memberBranches.value);
    }
  } catch (error) {
    console.error('Error fetching organization details:', error);
    toastMessage.value = '載入組織資料時發生錯誤';
  }
};

const loadPendingApplications = async () => {
  if (!isOwner.value) return;

  try {
    await organizationStore.fetchOrganizationBranchApplications(organizationId);
    pendingApplications.value = organizationStore.branchApplications;
  } catch (error) {
    console.error('Error fetching pending applications:', error);
    toastMessage.value = '載入申請資料時發生錯誤';
  }
};

const loadUserOwnedBranches = async () => {
  if (!authStore.isAuthenticated) return;

  try {
    const { data, error } = await supabase
      .from('branches')
      .select('id, name')
      .eq('owner_id', authStore.currentUser?.id);

    if (error) throw error;
    userOwnedBranches.value = data || [];
  } catch (error) {
    console.error('Error fetching user owned branches:', error);
  }
};

const checkApplicationStatus = async () => {
  if (!authStore.isAuthenticated || !isBranchOwner.value) return;

  try {
    // Check if any of the user's branches have applied to this organization
    const { data, error } = await supabase
      .from('organization_branch_applications')
      .select('*')
      .eq('organization_id', organizationId)
      .in('branch_id', userOwnedBranches.value.map(branch => branch.id))
      .eq('status', 'pending');

    if (error) throw error;
    hasApplied.value = data && data.length > 0;
  } catch (error) {
    console.error('Error checking application status:', error);
  }
};

const checkMembershipStatus = async () => {
  if (!authStore.isAuthenticated || !isBranchOwner.value) return;

  try {
    // Check if any of the user's branches are members of this organization
    const { data, error } = await supabase
      .from('organization_branches')
      .select('*')
      .eq('organization_id', organizationId)
      .in('branch_id', userOwnedBranches.value.map(branch => branch.id))
      .eq('status', 'active');

    if (error) throw error;
    isMember.value = data && data.length > 0;
  } catch (error) {
    console.error('Error checking membership status:', error);
  }
};

// Approve an application
const approveApplication = async (application: any) => {
  if (!isOwner.value) return;

  try {
    isProcessingApplication.value = true;
    await organizationStore.approveBranchApplication(application.id);

    // Refresh data
    await Promise.all([
      loadOrganizationDetails(),
      loadPendingApplications()
    ]);

    toastMessage.value = '已批准申請';
  } catch (error) {
    console.error('Error approving application:', error);
    toastMessage.value = '批准申請時發生錯誤';
  } finally {
    isProcessingApplication.value = false;
  }
};

// Reject an application
const rejectApplication = async (application: any) => {
  if (!isOwner.value) return;

  try {
    isProcessingApplication.value = true;
    await organizationStore.rejectBranchApplication(application.id);

    // Refresh pending applications
    await loadPendingApplications();

    toastMessage.value = '已拒絕申請';
  } catch (error) {
    console.error('Error rejecting application:', error);
    toastMessage.value = '拒絕申請時發生錯誤';
  } finally {
    isProcessingApplication.value = false;
  }
};

// Determine the referrer path based on document.referrer or navigation state
const determineReferrerPath = () => {
  // Check if we have a document.referrer
  const referrer = document.referrer;

  // If there's a referrer, extract the path
  if (referrer) {
    try {
      const url = new URL(referrer);
      const path = url.pathname;

      // Check if the path is one we want to handle
      if (path.includes('/home')) {
        referrerPath.value = '/home';
      } else if (path.includes('/organizations')) {
        referrerPath.value = '/organizations';
      } else if (path.includes('/branches')) {
        referrerPath.value = '/branches';
      }
    } catch (e) {
      console.error('Error parsing referrer URL:', e);
    }
  }

  // If we couldn't determine from referrer, check router history state
  if (window.history.state && window.history.state.back) {
    const backPath = window.history.state.back;
    if (backPath.includes('/home')) {
      referrerPath.value = '/home';
    } else if (backPath.includes('/organizations')) {
      referrerPath.value = '/organizations';
    } else if (backPath.includes('/branches')) {
      referrerPath.value = '/branches';
    }
  }
};

// Load related events for all branches in the organization
const loadRelatedEvents = async () => {
  try {
    // Get all branch owner IDs from the organization
    const branchOwnerIds = memberBranches.value
      .map(branch => branch.branches?.owner_id)
      .filter(id => id); // Filter out any undefined or null values

    if (branchOwnerIds.length === 0) {
      relatedEvents.value = [];
      return;
    }

    // Fetch events where the host is any of the branch owners
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        users:user_id (
          full_name
        )
      `)
      .in('user_id', branchOwnerIds)
      .order('start_datetime', { ascending: true });

    if (error) throw error;

    // Transform the data to include creator_full_name
    relatedEvents.value = data.map(event => ({
      ...event,
      creator_full_name: event.users?.full_name || null
    }));
  } catch (error) {
    console.error('Error fetching related events:', error);
    toastMessage.value = '載入相關活動時發生錯誤';
  }
};

// Load related shops for all branches in the organization
const loadRelatedShops = async () => {
  try {
    // Get all branch member user IDs from the organization
    const branchMemberUserIds = memberBranches.value.flatMap(branch =>
      branch.branches?.branch_members?.map(member => member.user_id) || []
    );

    if (branchMemberUserIds.length === 0) {
      relatedShops.value = [];
      return;
    }

    // Fetch shops owned by any branch member
    const { data, error } = await supabase
      .from('shops')
      .select(`
        *,
        shop_categories:shop_category_id(id, title)
      `)
      .in('owner_id', branchMemberUserIds)
      .order('created_at', { ascending: false });

    if (error) throw error;
    relatedShops.value = data || [];
  } catch (error) {
    console.error('Error fetching related shops:', error);
    toastMessage.value = '載入相關商店時發生錯誤';
  }
};

// Use Ionic lifecycle hooks
onIonViewDidEnter(async () => {
  isLoading.value = true;
  // Determine the referrer path
  determineReferrerPath();
  await loadOrganizationDetails();

  await Promise.all([
    loadUserOwnedBranches(),
  ]);

  // After loading user owned branches, check application and membership status
  await Promise.all([
    checkApplicationStatus(),
    checkMembershipStatus()
  ]);

  // Load pending applications if user is the organization owner
  if (isOwner.value) {
    await loadPendingApplications();
  }

  // Load related events and shops
  await Promise.all([
    loadRelatedEvents(),
    loadRelatedShops()
  ]);

  isLoading.value = false;
});

const handleOrganizationUpdated = (updatedOrganization: any) => {
  // Merge the updated data with existing data to preserve joined fields
  organization.value = {
    ...organization.value,
    ...updatedOrganization
  };
  showEditModal.value = false;
  toastMessage.value = '組織資料已更新';
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// More compact date format for the branches list
const formatCompactDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  }) + ' 加入';
};

const submitJoinApplication = async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  if (!selectedBranchId.value) {
    toastMessage.value = '請選擇要加入組織的分會';
    return;
  }

  try {
    isSubmittingApplication.value = true;
    await organizationStore.applyToOrganization(
      organizationId,
      selectedBranchId.value,
      applicationMessage.value
    );

    hasApplied.value = true;
    showJoinModal.value = false;
    applicationMessage.value = '';
    selectedBranchId.value = '';
    toastMessage.value = '申請已提交，請等待組織管理員審核';
  } catch (error: any) {
    console.error('Error submitting application:', error);
    toastMessage.value = error.message || '申請提交失敗，請稍後再試';
  } finally {
    isSubmittingApplication.value = false;
  }
};

// Show confirmation before approving application
const showApproveConfirm = (application: any) => {
  currentApplication.value = application;
  showApproveAlertFlag.value = true;
};

// Show confirmation before rejecting application
const showRejectConfirm = (application: any) => {
  currentApplication.value = application;
  showRejectAlertFlag.value = true;
};

// Handle join button click
const handleJoinButtonClick = () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    router.push('/login');
    return;
  }

  if (!isBranchOwner.value) {
    toastMessage.value = '只有分會長可以申請加入組織';
    return;
  }

  // User is logged in and is a branch owner, show the join modal
  showJoinModal.value = true;
};

// Check if a branch is owned by the organization owner (can't kick own branch)
const isOwnerBranch = (branch: any) => {
  return branch.branches?.owner_id === organization.value?.owner_id;
};

// Show confirmation before kicking a branch
const confirmKickBranch = async (branch: any) => {
  if (!branch || !isOwner.value || isOwnerBranch(branch)) return;

  const alert = await alertController.create({
    header: '移除分會',
    message: `確定要將 ${branch.branches?.name} 從組織中移除嗎？此操作無法撤銷。`,
    buttons: [
      {
        text: '取消',
        role: 'cancel'
      },
      {
        text: '移除',
        role: 'destructive',
        handler: () => {
          kickBranch(branch);
        }
      }
    ]
  });

  await alert.present();
};

// Kick branch from organization
const kickBranch = async (branch: any) => {
  if (!branch || !isOwner.value || isOwnerBranch(branch)) return;

  try {
    console.log('Kicking branch:', branch.branch_id, branch.branches?.name);
    await organizationStore.removeBranchFromOrganization(organizationId, branch.branch_id);
    console.log('Branch kicked successfully from database');

    // Remove branch from local state
    const beforeCount = memberBranches.value.length;
    memberBranches.value = memberBranches.value.filter(b => b.branch_id !== branch.branch_id);
    const afterCount = memberBranches.value.length;
    console.log(`Local state updated: ${beforeCount} -> ${afterCount} branches`);

    toastMessage.value = `已將 ${branch.branches?.name} 從組織中移除`;
  } catch (error) {
    console.error('Error kicking branch:', error);
    toastMessage.value = '移除分會時發生錯誤，請稍後再試';
  }
};
</script>

<style scoped>
.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.organization-container {
  max-width: 1200px;
  margin: 0 auto;
}

.organization-banner {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: hidden;
  border-radius: 24px;
  margin-bottom: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.organization-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.organization-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  display: flex;
  align-items: flex-end;
}

.organization-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
  flex-shrink: 0;
  margin-right: 1.5rem;
}

.organization-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.organization-details {
  flex: 1;
}

.organization-details h1 {
  margin: 0 0 0.5rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.organization-description {
  margin: 0 0 1rem;
  font-size: 1rem;
  opacity: 0.9;
}

.organization-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.meta-item ion-icon {
  margin-right: 0.5rem;
}

.organization-content {
  margin-top: 1rem;
}

.tab-content {
  padding: 0 0 1.5rem 0;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
}

.empty-text {
  color: var(--ion-color-medium);
  text-align: center;
  padding: 2rem 0;
}

.organization-owner-section {
  margin: 1rem 0;
  background-color: var(--ion-color-light);
  border-radius: 12px;
  overflow: hidden;
}

.organization-owner-item {
  --background: transparent;
}

.organization-owner-item ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.5rem;
}

.organization-owner-item h3 {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.organization-owner-item h2 {
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0;
}

.join-section {
  margin-top: 1rem;
  text-align: center;
}

.branches-container {
  display: flex;
  flex-direction: column;
}

.branches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.view-all-container {
  padding: 0.5rem;
  background-color: var(--ion-color-light);
  text-align: center;
  border-radius: 8px;
  margin-top: 1rem;
}

.branches-grid-modal,
.events-grid-modal,
.shops-grid-modal {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.shop-category-filter {
  margin-bottom: 1.5rem;
}

.shop-category-filter.modal-filter {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--ion-color-light-shade);
}

.shop-category-filter ion-segment {
  --background: var(--ion-color-light);
  border-radius: 8px;
  overflow: hidden;
}

.shop-category-filter ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-light);
  --background-checked: var(--ion-color-primary-tint);
  --border-radius: 6px;
  margin: 2px;
  font-size: 0.9rem;
}

.shop-category-filter ion-label {
  font-weight: 500;
}

.branch-card {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

.branch-card.compact {
  height: auto;
  position: relative;
}

.compact-branch-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.compact-branch-logo {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.compact-branch-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.compact-branch-info {
  flex: 1;
}

.compact-branch-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.compact-branch-info p {
  margin: 0;
  font-size: 14px;
  color: var(--ion-color-medium);
}

.branch-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.branch-banner {
  height: 150px;
  overflow: hidden;
}

.branch-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Applications section styles */
.applications-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--ion-color-light);
  border-radius: 12px;
}

.applications-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.application-card {
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.application-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .organization-banner {
    height: 250px;
    border-radius: 16px;
  }

  .organization-info-overlay {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .organization-logo {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .organization-meta {
    justify-content: center;
  }

  .branches-grid,
  .applications-list {
    grid-template-columns: 1fr;
  }
}
</style>
