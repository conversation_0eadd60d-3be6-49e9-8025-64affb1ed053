<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/events" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>活動詳情</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="shareEvent">
            <ion-icon :icon="shareOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button v-if="authStore.isAuthenticated && !isEventOwner" @click="toggleFavorite">
            <ion-icon :icon="isEventLiked ? heart : heartOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <template v-if="isEventOwner && event">
            <ion-button :router-link="`/events/${event.id}/check-in`">
              <ion-icon :icon="qrCodeOutline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button @click="openEditModal">
              <ion-icon :icon="createOutline" slot="icon-only"></ion-icon>
            </ion-button>
          </template>
          <template v-if="canDeleteEvent && event">
            <ion-button @click="confirmDelete">
              <ion-icon :icon="trashOutline" slot="icon-only"></ion-icon>
            </ion-button>
          </template>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else-if="event" class="event-container">
        <!-- Event Banner -->
        <div class="event-banner" v-if="event.banner_photo">
          <img :src="event.banner_photo" :alt="event.title">
        </div>

        <!-- Event Details -->
        <div class="event-content">
          <h1>{{ event.title }}</h1>

          <div class="event-meta-header">
            <div v-if="event.creator_full_name" class="event-creator">
              <ion-icon :icon="personOutline" color="medium"></ion-icon>
              <span>主辦人: {{ event.creator_full_name }}</span>
            </div>

            <div class="event-stats">
              <div class="event-stat-item" v-if="event.like_count !== undefined">
                <ion-icon :icon="heart" color="danger"></ion-icon>
                <span>{{ event.like_count || 0 }} 收藏</span>
              </div>
            </div>
          </div>

          <div class="event-meta">
            <ion-item lines="none">
              <ion-icon :icon="calendarOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>日期及時間</h2>
                <p>{{ formatDateTime(event.start_datetime) }}</p>
                <p>至 {{ formatDateTime(event.end_datetime) }}</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none">
              <ion-icon :icon="locationOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>地點</h2>
                <p>{{ event.address }}</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none" v-if="event.max_participants">
              <ion-icon :icon="peopleOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>人數上限</h2>
                <p>{{ event.max_participants }} 人 (已報名: {{ registrationCount }} 人, 尚餘 {{ remainingSpots }} 個名額)</p>
              </ion-label>
            </ion-item>
          </div>

          <div class="event-description">
            <h2>活動詳情</h2>
            <div v-html="event.description" style="white-space: pre-line"></div>
          </div>

          <!-- Event Photos Section -->
          <div v-if="eventPhotos.length > 0" class="event-photos-section">
            <h2>活動相片</h2>
            <div class="photos-grid">
              <div
                v-for="(photo, index) in eventPhotos"
                :key="photo.id"
                class="photo-item"
                @click="openPhotoModal(index)"
              >
                <img :src="photo.photo_url" :alt="photo.caption || `活動相片 ${index + 1}`" />
                <div v-if="photo.caption" class="photo-caption">{{ photo.caption }}</div>
              </div>
            </div>
          </div>

          <!-- Post-Event Photos Section (visible to everyone) -->
          <div v-if="postEventPhotos.length > 0 || (canManagePostEventPhotos && isPastEvent)" class="post-event-photos-section">
            <h2>活動記錄相片</h2>
            <p v-if="postEventPhotos.length > 0" class="section-description">活動結束後的記錄相片</p>

            <!-- Admin upload button (only for admins/owners of past events) -->
            <ion-button
              v-if="canManagePostEventPhotos && isPastEvent"
              expand="block"
              fill="outline"
              @click="addPostEventPhoto"
              class="add-photo-button"
            >
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              新增記錄相片
            </ion-button>

            <!-- Photos grid (visible to everyone) -->
            <div v-if="postEventPhotos.length > 0" class="photos-grid">
              <div
                v-for="(photo, index) in postEventPhotos"
                :key="photo.id"
                class="photo-item"
                :class="{ 'admin-photo': canManagePostEventPhotos }"
                @click="openPhotoModal(eventPhotos.length + index)"
              >
                <img :src="photo.photo_url" :alt="photo.caption || `記錄相片 ${index + 1}`" />
                <!-- Admin delete button (only for admins/owners) -->
                <div v-if="canManagePostEventPhotos" class="photo-actions">
                  <ion-button
                    fill="clear"
                    color="danger"
                    size="small"
                    @click.stop="deletePostEventPhoto(photo.id)"
                  >
                    <ion-icon :icon="trashOutline"></ion-icon>
                  </ion-button>
                </div>
                <div v-if="photo.caption" class="photo-caption">{{ photo.caption }}</div>
              </div>
            </div>

            <!-- Empty state for admins when no photos exist -->
            <div v-else-if="canManagePostEventPhotos && isPastEvent" class="empty-photos-state">
              <p>尚未上傳活動記錄相片</p>
            </div>
          </div>

          <!-- Registration Section -->
          <div class="registration-section">
            <!-- Show host message if user is the event owner -->
            <div v-if="isEventOwner" class="host-section">
              <h3>您是此活動的主辦人</h3>
              <p>作為主辦人，您已自動成為參與者，無需另行報名。</p>

              <!-- Applications List -->
              <div class="applications-section" v-if="applications.length > 0">
                <h3>報名名單 ({{ applications.length }} 人)</h3>
                <ion-list>
                  <ion-item v-for="app in applications" :key="app.id">
                    <ion-label>
                      <h2>{{ app.full_name }}</h2>
                      <p>{{ app.email }}</p>
                      <p>{{ app.phone }}</p>
                    </ion-label>
                    <ion-badge slot="end" :color="getStatusColor(app.status)">
                      {{ getStatusLabel(app.status) }}
                    </ion-badge>
                  </ion-item>
                </ion-list>
              </div>
              <div v-else class="empty-applications">
                <p>暫時沒有人報名</p>
              </div>
            </div>

            <!-- Show QR code if registered -->
            <div v-else-if="application" class="qr-code-section">
              <h3>您的入場二維碼</h3>
              <div class="qr-code">
                <img :src="qrCodeUrl" alt="QR Code" />
              </div>
              <p class="qr-note">請於活動當天出示此二維碼以供登記出席</p>
            </div>

            <!-- Show registration button if not registered and not the owner -->
            <template v-else-if="!isEventOwner">
              <ion-button
                expand="block"
                :router-link="hasAvailableSpots ? `/events/${event.id}/apply` : undefined"
                :disabled="!hasAvailableSpots"
              >
                {{ getRegistrationButtonText }}
              </ion-button>
              <p class="registration-note" v-if="event.max_participants">
                已報名：{{ registrationCount }} 人，尚餘名額：{{ remainingSpots }} 個
              </p>
            </template>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到活動</p>
      </div>

      <!-- Share Modal -->
      <ion-modal :is-open="showShareModal" @didDismiss="showShareModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>分享活動</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showShareModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="share-content">
            <h2>{{ event?.title }}</h2>
            <p>複製以下連結分享活動：</p>
            <div class="share-link">
              <ion-input readonly :value="shareableLink"></ion-input>
              <ion-button fill="clear" @click="copyShareLink">
                <ion-icon :icon="copyOutline"></ion-icon>
              </ion-button>
            </div>
            <div class="share-buttons">
              <ion-button expand="block" @click="shareToSystem">
                <ion-icon :icon="shareOutline" slot="start"></ion-icon>
                分享
              </ion-button>
            </div>
          </div>
        </ion-content>
      </ion-modal>

      <!-- Edit Event Modal -->
      <ion-modal :is-open="showEditModal" @didDismiss="showEditModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>編輯活動</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showEditModal = false">取消</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <EventForm
            :initial-data="editedEvent"
            mode="edit"
            :is-submitting="isSubmitting"
            @update:initial-data="editedEvent = $event"
            @submit="handleEditEvent"
          />
        </ion-content>
      </ion-modal>

      <!-- Delete Confirmation Alert -->
      <ion-alert
        :is-open="showDeleteAlert"
        header="確認刪除活動"
        message="確定要刪除此活動嗎？此操作無法撤銷。"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => {
              showDeleteAlert = false;
            },
          },
          {
            text: '確定刪除',
            role: 'destructive',
            handler: () => {
              handleDeleteEvent();
            },
          },
        ]"
      ></ion-alert>

      <!-- Photo Modal -->
      <ion-modal :is-open="showPhotoModal" @didDismiss="showPhotoModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>活動相片</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showPhotoModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="photo-modal-content">
          <div v-if="allPhotos[selectedPhotoIndex]" class="photo-modal-container">
            <img
              :src="allPhotos[selectedPhotoIndex].photo_url"
              :alt="allPhotos[selectedPhotoIndex].caption || '活動相片'"
              class="modal-photo"
            />
            <div v-if="allPhotos[selectedPhotoIndex].caption" class="modal-photo-caption">
              {{ allPhotos[selectedPhotoIndex].caption }}
            </div>

            <!-- Navigation buttons -->
            <ion-button
              v-if="selectedPhotoIndex > 0"
              fill="clear"
              class="nav-button prev-button"
              @click="selectedPhotoIndex--"
            >
              <ion-icon :icon="chevronBackOutline" size="large"></ion-icon>
            </ion-button>

            <ion-button
              v-if="selectedPhotoIndex < allPhotos.length - 1"
              fill="clear"
              class="nav-button next-button"
              @click="selectedPhotoIndex++"
            >
              <ion-icon :icon="chevronForwardOutline" size="large"></ion-icon>
            </ion-button>
          </div>
        </ion-content>
      </ion-modal>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonBadge,
  IonToast,
  IonAlert,
  IonModal,
  IonInput,
  IonTextarea,
  IonDatetime,
  IonDatetimeButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  calendarOutline,
  locationOutline,
  peopleOutline,
  personOutline,
  qrCodeOutline,
  createOutline,
  trashOutline,
  alertCircleOutline,
  shareOutline,
  copyOutline,
  heart,
  heartOutline,
  cameraOutline,
  chevronBackOutline,
  chevronForwardOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import { Share } from '@capacitor/share';
import { Clipboard } from '@capacitor/clipboard';
import EventForm from '@/components/EventForm.vue';
import { usePhotoGallery } from '@/composables/usePhotoGallery';
import { uploadImages } from '@/lib/cloudflare';

// Format datetime for display (assuming input is already in HKT)
const formatDateTime = (datetimeStr: string) => {
  if (!datetimeStr) return '';

  // Create a date object from the datetime string
  const date = new Date(datetimeStr);

  // Format the date
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const eventId = route.params.id as string;

const event = ref<any>(null);
const application = ref<any>(null);
const applications = ref<any[]>([]);
const isLoading = ref(true);
const isSubmitting = ref(false);
const toastMessage = ref('');
const showDeleteAlert = ref(false);
const showShareModal = ref(false);
const showEditModal = ref(false);
const registrationCount = ref(0);

// Photo gallery
const { takePhoto } = usePhotoGallery();

// Event photos
const eventPhotos = ref<any[]>([]);
const postEventPhotos = ref<any[]>([]);
const selectedPhotoIndex = ref(0);
const showPhotoModal = ref(false);


// For edit event form
const editedEvent = ref<any>({
  title: '',
  start_datetime: '',
  end_datetime: '',
  address: '',
  description: '',
  max_participants: null
});

const isEventOwner = computed(() => {
  return event.value?.user_id === authStore.currentUser?.id;
});

const isBranchOwnerCanDelete = computed(() => {
  if (!event.value?.branch_id || !authStore.currentUser?.id) return false;

  // Check if current user owns the branch that this event belongs to
  return userStore.userBranches.some(branch => branch.id === event.value.branch_id);
});

const canDeleteEvent = computed(() => {
  return isEventOwner.value || isBranchOwnerCanDelete.value;
});

const isEventLiked = computed(() => {
  return userStore.isEventLiked(eventId);
});

const remainingSpots = computed(() => {
  if (!event.value?.max_participants) return '無限制';
  return event.value.max_participants - registrationCount.value;
});

const hasAvailableSpots = computed(() => {
  if (!event.value?.max_participants) return true;
  return registrationCount.value < event.value.max_participants;
});

const getRegistrationButtonText = computed(() => {
  if (application.value) return '已報名';
  if (!hasAvailableSpots.value) return '名額已滿';
  return '立即報名';
});

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'pending':
      return '待確認';
    case 'confirmed':
      return '已確認';
    case 'cancelled':
      return '已取消';
    case 'attended':
      return '已出席';
    default:
      return status;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'confirmed':
      return 'success';
    case 'cancelled':
      return 'medium';
    case 'attended':
      return 'primary';
    default:
      return 'medium';
  }
};

const shareableLink = computed(() => {
  if (!event.value?.id) return '';
  //return `${window.location.origin}/events/${event.value.id}`;
  return `https://syner-biz.com/events/${event.value.id}`;
});

const qrCodeUrl = computed(() => {
  if (!application.value?.qr_code) return '';
  return `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(application.value.qr_code)}&dpi=96&quietzone=5`;
});

// Check if event is in the past
const isPastEvent = computed(() => {
  if (!event.value?.end_datetime) return false;
  return new Date(event.value.end_datetime) < new Date();
});

// Check if user can manage post-event photos (admin or event owner)
const canManagePostEventPhotos = computed(() => {
  if (!authStore.currentUser) return false;
  return authStore.currentUser.is_admin || isEventOwner.value;
});

onIonViewDidEnter(async () => {
  await loadEventDetails();
});

const loadEventDetails = async () => {
  try {
    isLoading.value = true;

    // Load event details with creator and branch information
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select(`
        *,
        users:user_id (
          full_name
        ),
        branches:branch_id (
          id,
          name,
          owner_id
        )
      `)
      .eq('id', eventId)
      .single();

    if (eventError) throw eventError;

    // Add creator_full_name and branch info to the event object
    event.value = {
      ...eventData,
      creator_full_name: eventData.users?.full_name || null,
      branch_name: eventData.branches?.name || null,
      branch_owner_id: eventData.branches?.owner_id || null
    };

    // Check if user is registered
    if (authStore.currentUser?.id) {
      const { data: applicationData, error: applicationError } = await supabase
        .from('event_applications')
        .select('*')
        .eq('event_id', eventId)
        .eq('user_id', authStore.currentUser.id)
        .maybeSingle();

      if (applicationError) {
        console.error('Error checking application status:', applicationError);
      } else {
        application.value = applicationData;
      }

      // If user is the event owner, load all applications
      if (eventData.user_id === authStore.currentUser.id) {
        const { data: applicationsData, error: applicationsError } = await supabase
          .from('event_applications')
          .select('*')
          .eq('event_id', eventId)
          .order('created_at', { ascending: true });

        if (applicationsError) {
          console.error('Error loading applications:', applicationsError);
        } else {
          applications.value = applicationsData || [];
        }
      }
    }

    // Get registration count
    const { count } = await supabase
      .from('event_applications')
      .select('id', { count: 'exact' })
      .eq('event_id', eventId)
      .in('status', ['confirmed', 'attended']);

    registrationCount.value = count || 0;

    // Load event photos
    await loadEventPhotos();
  } catch (error) {
    console.error('Error loading event:', error);
    toastMessage.value = '載入活動資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Function moved to the top of the file

const confirmDelete = () => {
  showDeleteAlert.value = true;
};

const handleDeleteEvent = async () => {
  try {
    isSubmitting.value = true;

    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', eventId);

    if (error) throw error;

    toastMessage.value = '活動已刪除';

    // Navigate back to events page after a short delay
    setTimeout(() => {
      router.replace('/events');
    }, 1000);
  } catch (error) {
    console.error('Error deleting event:', error);
    toastMessage.value = '刪除活動時發生錯誤';
  } finally {
    isSubmitting.value = false;
    showDeleteAlert.value = false;
  }
};

const shareEvent = () => {
  showShareModal.value = true;
};

const copyShareLink = async () => {
  try {
    await Clipboard.write({ string: shareableLink.value });
    toastMessage.value = '連結已複製';
  } catch (error) {
    try {
      await navigator.clipboard.writeText(shareableLink.value);
      toastMessage.value = '連結已複製';
    } catch (e) {
      console.error('Failed to copy:', error);
      toastMessage.value = '複製失敗，請手動複製';
    }
  }
};

const toggleFavorite = async () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以收藏活動';
    return;
  }

  const result = await userStore.toggleEventFavorite(eventId);

  // Display the result message
  if (result.message) {
    toastMessage.value = result.message;
  }

  // Update UI state if successful
  if (result.success) {
    // Update like count in UI
    if (isEventLiked.value) {
      // Just added to favorites
      event.value.like_count = (event.value.like_count || 0) + 1;
    } else {
      // Just removed from favorites
      if (event.value.like_count && event.value.like_count > 0) {
        event.value.like_count -= 1;
      }
    }
  }
};

const shareToSystem = async () => {
  if (!event.value) return;

  try {
    await Share.share({
      title: '活動邀請',
      text: `邀請您參加「${event.value.title}」`,
      url: shareableLink.value,
      dialogTitle: '分享活動',
    });
  } catch (error) {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '活動邀請',
          text: `邀請您參加「${event.value.title}」
${shareableLink.value}`,
        });
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
          toastMessage.value = '分享失敗';
        }
      }
    } else {
      await copyShareLink();
    }
  }
};



const openEditModal = () => {
  if (!event.value) return;

  // Set the edited event data using the datetime fields directly
  editedEvent.value = {
    title: event.value.title || '',
    start_datetime: event.value.start_datetime || '',
    end_datetime: event.value.end_datetime || '',
    address: event.value.address || '',
    description: event.value.description || '',
    max_participants: event.value.max_participants || null
  };

  showEditModal.value = true;
};

const handleEditEvent = async (formData: any) => {
  try {
    isSubmitting.value = true;

    // Use datetime values directly
    const eventData = {
      title: formData.title,
      start_datetime: formData.start_datetime,
      end_datetime: formData.end_datetime,
      address: formData.address,
      description: formData.description,
      max_participants: formData.max_participants || null
    };

    console.log('Updating event with:', eventData);

    const { error } = await supabase
      .from('events')
      .update(eventData)
      .eq('id', eventId);

    if (error) throw error;

    // Update the local event data
    event.value = { ...event.value, ...eventData };

    showEditModal.value = false;
    toastMessage.value = '活動已更新';

    // Reload event details to ensure everything is up to date
    await loadEventDetails();
  } catch (error) {
    console.error('Error updating event:', error);
    toastMessage.value = '更新活動時發生錯誤';
  } finally {
    isSubmitting.value = false;
  }
};

// Load event photos
const loadEventPhotos = async () => {
  try {
    const { data, error } = await supabase
      .from('event_photos')
      .select('*')
      .eq('event_id', eventId)
      .order('order', { ascending: true });

    if (error) throw error;

    // Separate creation and post-event photos
    eventPhotos.value = data.filter(photo => photo.photo_type === 'creation') || [];
    postEventPhotos.value = data.filter(photo => photo.photo_type === 'post_event') || [];
  } catch (error) {
    console.error('Error loading event photos:', error);
  }
};

// Combined photos for modal viewing
const allPhotos = computed(() => {
  return [...eventPhotos.value, ...postEventPhotos.value];
});

// Photo modal functions
const openPhotoModal = (index: number) => {
  selectedPhotoIndex.value = index;
  showPhotoModal.value = true;
};

// Add post-event photo
const addPostEventPhoto = async () => {
  try {
    const photo = await takePhoto();
    if (!photo || !photo.base64Data) return;

    // Upload photo
    const imageInputs = [{
      base64Data: photo.base64Data,
      filename: `event_${eventId}_post_${new Date().getTime()}.jpg`,
      mimeType: photo.mimeType || 'image/jpeg'
    }];

    const uploadedUrls = await uploadImages(imageInputs);
    if (uploadedUrls.length === 0) throw new Error('Photo upload failed');

    // Save to database
    const { data, error } = await supabase
      .from('event_photos')
      .insert({
        event_id: eventId,
        photo_url: uploadedUrls[0],
        caption: '',
        order: postEventPhotos.value.length,
        photo_type: 'post_event',
        uploaded_by: authStore.currentUser?.id
      })
      .select()
      .single();

    if (error) throw error;

    postEventPhotos.value.push(data);
    toastMessage.value = '相片上傳成功';
  } catch (error) {
    console.error('Error adding post-event photo:', error);
    toastMessage.value = '相片上傳失敗，請稍後再試';
  }
};

// Delete post-event photo
const deletePostEventPhoto = async (photoId: string) => {
  try {
    const { error } = await supabase
      .from('event_photos')
      .delete()
      .eq('id', photoId);

    if (error) throw error;

    postEventPhotos.value = postEventPhotos.value.filter(photo => photo.id !== photoId);
    toastMessage.value = '相片已刪除';
  } catch (error) {
    console.error('Error deleting post-event photo:', error);
    toastMessage.value = '刪除相片失敗，請稍後再試';
  }
};
</script>

<style scoped>
.event-container {
  max-width: 800px;
  margin: 0 auto;
}

.event-banner {
  position: relative;
  width: 100%;
  height: 300px;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.event-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.event-content h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--ion-color-dark);
}

.event-creator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: var(--ion-color-medium);
  font-size: 0.95rem;
}

.event-meta {
  margin-bottom: 2rem;
}

.event-meta ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 1rem;
}

.event-meta ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.5rem;
  margin-right: 1rem;
}

.event-meta h2 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-medium);
}

.event-meta p {
  font-size: 1.1rem;
  margin: 0.25rem 0 0;
  color: var(--ion-color-dark);
}

.event-description {
  margin-bottom: 2rem;
}

.event-description h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.registration-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--ion-color-light);
}

.registration-note {
  text-align: center;
  margin: 1rem 0 0;
  color: var(--ion-color-medium);
}

.qr-code-section {
  text-align: center;
  margin-bottom: 2rem;
}

.qr-code-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.qr-code {
  display: inline-block;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.qr-code img {
  width: 200px;
  height: 200px;
}

.qr-note {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  margin: 1rem 0;
}

.error-container {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.share-content {
  padding: 1rem;
}

.share-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.share-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  background: var(--ion-color-light);
  border-radius: 8px;
  padding: 0.5rem;
}

.share-link ion-input {
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --background: transparent;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.event-meta-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.event-creator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-medium);
}

.event-stats {
  display: flex;
  gap: 16px;
}

.event-stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ion-color-medium);
}

@media (max-width: 768px) {
  .event-banner {
    height: 200px;
    border-radius: 16px;
  }

  .event-content {
    padding: 1.5rem;
  }

  .event-content h1 {
    font-size: 1.5rem;
  }

  .event-meta ion-icon {
    font-size: 1.25rem;
  }

  .qr-code img {
    width: 160px;
    height: 160px;
  }
}

/* Event Photos Styles */
.event-photos-section,
.post-event-photos-section {
  margin-bottom: 2rem;
}

.event-photos-section h2,
.post-event-photos-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.section-description {
  color: var(--ion-color-medium);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.photo-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
}

.photo-item:hover {
  transform: scale(1.02);
}

.photo-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.photo-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 0.5rem;
  font-size: 0.8rem;
}

.admin-photo .photo-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.add-photo-button {
  margin-bottom: 1rem;
}

.empty-photos-state {
  text-align: center;
  padding: 2rem;
  color: var(--ion-color-medium);
  font-style: italic;
}

@media (max-width: 768px) {
  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
  }

  .photo-item img {
    height: 120px;
  }
}

/* Photo Modal Styles */
.photo-modal-content {
  --background: #000;
}

.photo-modal-container {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-photo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.modal-photo-caption {
  position: absolute;
  bottom: 2rem;
  left: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  width: 50px;
  height: 50px;
}

.prev-button {
  left: 1rem;
}

.next-button {
  right: 1rem;
}

@media (max-width: 768px) {
  .nav-button {
    width: 40px;
    height: 40px;
  }

  .modal-photo-caption {
    bottom: 1rem;
    left: 0.5rem;
    right: 0.5rem;
    padding: 0.75rem;
  }
}
</style>