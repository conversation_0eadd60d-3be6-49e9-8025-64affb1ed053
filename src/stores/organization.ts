import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from './auth';

// Define a type for organization branch applications
interface OrganizationBranchApplication {
  id: string;
  organization_id: string;
  branch_id: string;
  status: string;
  message?: string;
  created_at: string;
}

export const useOrganizationStore = defineStore('organization', () => {
  const organizations = ref<any[]>([]);
  const userOrganizations = ref<any[]>([]);
  const joinedOrganizations = ref<any[]>([]);
  const isLoading = ref(false);
  const branchApplications = ref<OrganizationBranchApplication[]>([]);
  const authStore = useAuthStore();

  // Fetch all organizations
  const fetchOrganizations = async () => {
    try {
      isLoading.value = true;
      const { data, error } = await supabase
        .from('organizations')
        .select(`
          *,
          owner:owner_id (
            id,
            full_name,
            username
          ),
          organization_branches!organization_branches_organization_id_fkey (
            branch_id,
            status,
            branches (
              id,
              name,
              logo
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      organizations.value = data || [];
    } catch (error) {
      console.error('Error fetching organizations:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // Fetch organizations owned by the current user
  const fetchUserOrganizations = async () => {
    if (!authStore.isAuthenticated) {
      userOrganizations.value = [];
      return;
    }

    try {
      isLoading.value = true;
      const { data, error } = await supabase
        .from('organizations')
        .select(`
          *,
          organization_branches!organization_branches_organization_id_fkey (
            branch_id,
            status,
            branches (
              id,
              name,
              logo
            )
          )
        `)
        .eq('owner_id', authStore.currentUser?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      userOrganizations.value = data || [];
    } catch (error) {
      console.error('Error fetching user organizations:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // Fetch organizations that the user's branch has joined
  const fetchJoinedOrganizations = async () => {
    if (!authStore.isAuthenticated) {
      joinedOrganizations.value = [];
      return;
    }

    try {
      isLoading.value = true;

      // First get the user's branch ID
      const { data: branchData, error: branchError } = await supabase
        .from('branches')
        .select('id')
        .eq('owner_id', authStore.currentUser?.id)
        .maybeSingle();

      if (branchError) throw branchError;

      if (!branchData) {
        joinedOrganizations.value = [];
        return;
      }

      // Then get organizations that this branch has joined
      const { data, error } = await supabase
        .from('organization_branches')
        .select(`
          joined_at,
          status,
          organizations (
            *,
            owner:owner_id (
              id,
              full_name,
              username
            )
          )
        `)
        .eq('branch_id', branchData.id)
        .eq('status', 'active')
        .order('joined_at', { ascending: false });

      if (error) throw error;
      joinedOrganizations.value = data || [];
    } catch (error) {
      console.error('Error fetching joined organizations:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // Fetch branch applications for organizations owned by the current user
  const fetchOrganizationBranchApplications = async (organizationId: string) => {
    if (!authStore.isAuthenticated) {
      branchApplications.value = [];
      return;
    }

    try {
      const { data, error } = await supabase
        .from('organization_branch_applications')
        .select(`
          *,
          branches (
            id,
            name,
            logo,
            description,
            owner_id,
            branch_members!branch_members_branch_id_fkey (
              user_id,
              users (
                id,
                full_name
              )
            )
          )
        `)
        .eq('organization_id', organizationId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      branchApplications.value = data || [];
    } catch (error) {
      console.error('Error fetching organization branch applications:', error);
    }
  };

  // Check if a branch has a pending application for an organization
  const hasPendingApplication = async (organizationId: string, branchId: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_branch_applications')
        .select('id')
        .eq('organization_id', organizationId)
        .eq('branch_id', branchId)
        .eq('status', 'pending')
        .maybeSingle();

      if (error) throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking pending application:', error);
      return false;
    }
  };

  // Check if a branch is a member of an organization
  const isBranchMember = async (organizationId: string, branchId: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_branches')
        .select('id')
        .eq('organization_id', organizationId)
        .eq('branch_id', branchId)
        .eq('status', 'active')
        .maybeSingle();

      if (error) throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking branch membership:', error);
      return false;
    }
  };

  // Create a new organization
  const createOrganization = async (organizationData: any) => {
    if (!authStore.isAuthenticated) {
      throw new Error('User must be authenticated to create an organization');
    }

    try {
      const newOrganization = {
        ...organizationData,
        owner_id: authStore.currentUser?.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('organizations')
        .insert(newOrganization)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  };

  // Update an organization
  const updateOrganization = async (organizationId: string, updates: any) => {
    if (!authStore.isAuthenticated) {
      throw new Error('User must be authenticated to update an organization');
    }

    try {
      const { data, error } = await supabase
        .from('organizations')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', organizationId)
        .eq('owner_id', authStore.currentUser?.id) // Ensure only owner can update
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating organization:', error);
      throw error;
    }
  };

  // Apply to join an organization with a branch
  const applyToOrganization = async (organizationId: string, branchId: string, message: string) => {
    if (!authStore.isAuthenticated) {
      throw new Error('User must be authenticated to apply to an organization');
    }

    try {
      const { data, error } = await supabase
        .from('organization_branch_applications')
        .insert({
          organization_id: organizationId,
          branch_id: branchId,
          message,
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error applying to organization:', error);
      throw error;
    }
  };

  // Approve a branch application
  const approveBranchApplication = async (applicationId: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_branch_applications')
        .update({
          status: 'approved',
          updated_at: new Date().toISOString()
        })
        .eq('id', applicationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error approving branch application:', error);
      throw error;
    }
  };

  // Reject a branch application
  const rejectBranchApplication = async (applicationId: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_branch_applications')
        .update({
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', applicationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error rejecting branch application:', error);
      throw error;
    }
  };

  // Get organization by ID
  const getOrganizationById = async (organizationId: string) => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select(`
          *,
          owner:owner_id (
            id,
            full_name,
            username
          ),
          organization_branches!organization_branches_organization_id_fkey (
            branch_id,
            joined_at,
            status,
            branches (
              id,
              name,
              logo,
              description,
              district,
              member_count,
              created_at,
              owner_id,
              branch_members!branch_members_branch_id_fkey (
                user_id,
                users (
                  id,
                  full_name
                )
              )
            )
          )
        `)
        .eq('id', organizationId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching organization by ID:', error);
      throw error;
    }
  };

  // Check if user is an organization owner
  const isOrganizationOwner = (organizationId: string) => {
    if (!authStore.isAuthenticated) return false;

    const organization = organizations.value.find(org => org.id === organizationId);
    return organization?.owner_id === authStore.currentUser?.id;
  };

  // Check if user is a branch owner
  const isBranchOwner = (branchId: string) => {
    if (!authStore.isAuthenticated) return false;

    // This would need to be implemented with a query to check if the user is the owner of the branch
    // For now, we'll return false and implement this later
    return false;
  };

  // Remove (kick) a branch from an organization
  const removeBranchFromOrganization = async (organizationId: string, branchId: string) => {
    if (!authStore.isAuthenticated) {
      throw new Error('User must be authenticated to remove a branch from organization');
    }

    try {
      // Option 1: Update status to 'inactive' (current implementation - maintains audit trail)
      const { error } = await supabase
        .from('organization_branches')
        .update({
          status: 'inactive',
          updated_at: new Date().toISOString()
        })
        .eq('organization_id', organizationId)
        .eq('branch_id', branchId);

      // Option 2: Delete the row directly (uncomment this and comment above if preferred)
      // const { error } = await supabase
      //   .from('organization_branches')
      //   .delete()
      //   .eq('organization_id', organizationId)
      //   .eq('branch_id', branchId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error removing branch from organization:', error);
      throw error;
    }
  };

  return {
    organizations,
    userOrganizations,
    joinedOrganizations,
    isLoading,
    branchApplications,
    fetchOrganizations,
    fetchUserOrganizations,
    fetchJoinedOrganizations,
    fetchOrganizationBranchApplications,
    hasPendingApplication,
    isBranchMember,
    createOrganization,
    updateOrganization,
    applyToOrganization,
    approveBranchApplication,
    rejectBranchApplication,
    getOrganizationById,
    isOrganizationOwner,
    isBranchOwner,
    removeBranchFromOrganization
  };
});
