/*
  # Add Branch Event Permissions

  1. Changes
    - Add branch_id column to events table to link events to branches
    - Update RLS policies to allow branch owners to delete events created by their branch members
    - Add indexes for better query performance
    
  2. Security
    - Update existing RLS policies for events
    - Allow branch owners to manage events created by their branch members
*/

-- Add branch_id column to events table
ALTER TABLE events
ADD COLUMN IF NOT EXISTS branch_id uuid REFERENCES branches(id);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS events_branch_id_idx ON events(branch_id);

-- Drop existing event policies to recreate them with branch permissions
DROP POLICY IF EXISTS "Anyone can view events" ON events;
DROP POLICY IF EXISTS "Users can manage their own events" ON events;

-- Recreate policies with branch owner permissions

-- Anyone can view events
CREATE POLICY "Anyone can view events"
  ON events
  FOR SELECT
  TO authenticated
  USING (true);

-- Users can create their own events
CREATE POLICY "Users can create their own events"
  ON events
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own events
CREATE POLICY "Users can update their own events"
  ON events
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete their own events OR branch owners can delete events created by their branch members
CREATE POLICY "Users can delete their own events or branch owners can delete member events"
  ON events
  FOR DELETE
  TO authenticated
  USING (
    auth.uid() = user_id OR
    (
      branch_id IS NOT NULL AND
      EXISTS (
        SELECT 1 FROM branches
        WHERE branches.id = events.branch_id
        AND branches.owner_id = auth.uid()
      )
    )
  );
