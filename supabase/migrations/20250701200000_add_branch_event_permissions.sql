/*
  # Add Branch Event Permissions

  1. Changes
    - Update RLS policies to allow branch owners to delete events created by their branch members
    - Use joined queries to check if event creator is a member of the branch

  2. Security
    - Update existing RLS policies for events
    - Allow branch owners to manage events created by their branch members
*/

-- Drop existing event policies to recreate them with branch permissions
DROP POLICY IF EXISTS "Anyone can view events" ON events;
DROP POLICY IF EXISTS "Users can manage their own events" ON events;

-- Recreate policies with branch owner permissions

-- Anyone can view events
CREATE POLICY "Anyone can view events"
  ON events
  FOR SELECT
  TO authenticated
  USING (true);

-- Users can create their own events
CREATE POLICY "Users can create their own events"
  ON events
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own events
CREATE POLICY "Users can update their own events"
  ON events
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete their own events OR branch owners can delete events created by their branch members
CREATE POLICY "Users can delete their own events or branch owners can delete member events"
  ON events
  FOR DELETE
  TO authenticated
  USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM branches b
      JOIN branch_members bm ON b.id = bm.branch_id
      WHERE b.owner_id = auth.uid()
      AND bm.user_id = events.user_id
      AND bm.status = 'active'
    )
  );
