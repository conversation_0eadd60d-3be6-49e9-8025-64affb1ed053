# 商聯思維 (Synerthink) - Business Networking Platform

A comprehensive business networking platform built for Hong Kong merchants and professionals to connect, collaborate, and grow their businesses together.

## 📱 Project Overview

**商聯思維 (Synerthink)** is a mobile-first business networking application that enables:
- Professional networking and business connections
- Branch/organization management with hierarchical structure
- Event creation and management
- Product/service showcasing through shop profiles
- Real-time messaging and communication
- User interaction tracking and analytics

### Target Audience
- Hong Kong merchants and business professionals
- Industry-specific branch organizations
- Service providers and product sellers
- Business event organizers

## 🏗️ Architecture & Tech Stack

### Frontend
- **Framework**: Vue 3 + TypeScript + Vite
- **Mobile Framework**: Ionic 7 + Capacitor
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **UI Components**: Ionic Components
- **Rich Text Editor**: QuillJS (migrated from vue-quill)
- **Image Handling**: Capacitor Camera Plugin
- **Push Notifications**: OneSignal
- **OTA Updates**: @capgo/capacitor-updater

### Backend & Database
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Realtime (for messaging)
- **File Storage**: Supabase Storage
- **API**: Supabase REST API with Row Level Security (RLS)

### Development Tools
- **Package Manager**: npm
- **Build Tool**: Vite
- **Code Quality**: TypeScript, ESLint
- **Version Control**: Git

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Supabase account
- Ionic CLI: `npm install -g @ionic/cli`
- Capacitor CLI: `npm install -g @capacitor/cli`

### Installation
```bash
# Clone the repository
git clone https://github.com/mlolpet/syner-biz.git
cd syner-biz

# Install dependencies
npm install

# Set up environment variables (see Environment Setup below)
cp .env.example .env.local

# Start development server
npm run dev

# For mobile development
ionic serve
```

### Environment Setup
Create `.env.local` file with the following variables:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_ONESIGNAL_APP_ID=your_onesignal_app_id
```

## 📊 Database Schema Overview

### Core Tables
- **users**: User profiles and authentication data
- **branches**: Organization/branch information
- **branch_members**: Branch membership with status tracking
- **shops**: Business shop profiles
- **products**: Product/service listings
- **events**: Event management
- **conversations**: Chat conversations
- **messages**: Chat messages
- **user_liked_***: Like/favorite functionality for various entities
- **user_interaction_logs**: User activity tracking

### Key Relationships
- Users can own multiple branches and shops
- Users can be members of multiple branches
- Branch owners have administrative privileges
- Events can be created by branch members
- Real-time messaging between users

## 🎯 Key Features

### User Management
- **Authentication**: Supabase Auth with email/password
- **User Profiles**: Comprehensive profile management with avatars
- **Role-based Access**: Different permissions for branch owners/members
- **Activity Tracking**: User interaction logs for analytics

### Branch System
- **Hierarchical Organization**: Branches can have parent-child relationships
- **Membership Management**: Join requests, approvals, member removal
- **Branch Profiles**: Detailed information, descriptions, contact details
- **Administrative Controls**: Branch owners can manage members and content

### Shop & Product Management
- **Shop Profiles**: Comprehensive business information
- **Product Catalog**: Image galleries, descriptions, categories
- **Search & Filter**: Category-based filtering and search functionality
- **Like/Favorite System**: Users can like shops and products

### Event Management
- **Event Creation**: Support for single and multi-date events
- **Registration System**: Event applications with host approval
- **Photo Management**: Event photos for records and promotion
- **Email Notifications**: Automated notifications for applications

### Messaging System
- **Real-time Chat**: Supabase Realtime for instant messaging
- **Read Status**: WhatsApp-style read indicators
- **Push Notifications**: OneSignal integration for mobile notifications
- **Conversation Management**: Organized chat history

### Mobile Features
- **Responsive Design**: Mobile-first approach with Ionic
- **Camera Integration**: Photo capture and upload
- **Push Notifications**: Background notifications
- **Offline Support**: Basic offline functionality
- **OTA Updates**: Over-the-air app updates

## 🏛️ Project Structure

```
src/
├── components/          # Reusable Vue components
│   ├── BranchFormModal.vue
│   ├── ShopCard.vue
│   ├── EventCard.vue
│   └── ...
├── views/              # Page components
│   ├── HomePage.vue
│   ├── BranchDetailPage.vue
│   ├── ShopDetailPage.vue
│   └── ...
├── stores/             # Pinia state management
│   ├── auth.ts
│   ├── user.ts
│   └── ...
├── lib/                # Utility libraries
│   ├── supabase.ts
│   └── utils.ts
├── router/             # Vue Router configuration
└── assets/             # Static assets
```

## 🔧 Development Guidelines

### Code Style & Conventions
- **Language**: TypeScript for type safety
- **Component Style**: Vue 3 Composition API with `<script setup>`
- **Naming**: snake_case for database fields, camelCase for JavaScript
- **Database**: Prefer snake_case naming convention
- **UI Framework**: Ionic components for consistent mobile experience

### Database Best Practices
- **RLS Policies**: Implement Row Level Security for data protection
- **Triggers**: Use database triggers for automatic updates (counters, timestamps)
- **Joins**: Prefer efficient join queries over multiple API calls
- **Constraints**: Maintain data integrity with proper constraints
- **Timezone**: Store in UTC, display in GMT+8 (Hong Kong time)

### State Management
- **Pinia Stores**: Use for global state management
- **Actions**: Implement joined table queries in store actions
- **Reactivity**: Leverage Vue 3 reactivity for real-time updates

### UI/UX Principles
- **Mobile-First**: Design for mobile, enhance for desktop
- **Minimal Design**: Clean, compact interfaces
- **User Feedback**: Clear error messages and success notifications
- **Accessibility**: Proper labeling and navigation
- **Performance**: Optimize for mobile networks

## 🚀 Deployment

### Web Deployment
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

### Mobile App Deployment
```bash
# Build and sync with Capacitor
ionic build
ionic cap sync

# iOS
ionic cap open ios

# Android
ionic cap open android
```

### Environment Configuration
- **Development**: `.env.local`
- **Production**: Configure environment variables in your hosting platform
- **Supabase**: Set up RLS policies and database triggers
- **OneSignal**: Configure push notification certificates

## 📱 Mobile Development

### iOS Setup
1. Install Xcode and iOS development tools
2. Configure Apple Developer account
3. Set up push notification certificates in OneSignal
4. Configure app signing and provisioning profiles

### Android Setup
1. Install Android Studio
2. Configure Android SDK
3. Set up Google Play Console for app distribution
4. Configure push notification keys in OneSignal

### Capacitor Plugins Used
- `@capacitor/camera` - Photo capture and selection
- `@capacitor/share` - Native sharing functionality
- `@capgo/capacitor-updater` - Over-the-air updates
- `@capacitor/push-notifications` - Push notifications (via OneSignal)

## 🔍 Important Implementation Notes

### Authentication & Security
- **Supabase Auth**: Email/password authentication with JWT tokens
- **RLS Policies**: Ensure users can only access authorized data
- **API Security**: Never expose service keys in client-side code
- **Data Validation**: Implement both client and server-side validation

### Database Considerations
- **Migration Strategy**: Use Supabase migrations for schema changes
- **Backup Strategy**: Regular database backups recommended
- **Performance**: Monitor query performance and optimize as needed
- **Constraints**: Current status constraints in `branch_members` table:
  - Allowed values: 'active', 'inactive', 'pending', 'rejected'

### Real-time Features
- **Supabase Realtime**: Used for chat messaging
- **Connection Management**: Handle connection drops gracefully
- **Subscription Cleanup**: Properly unsubscribe from realtime channels

### File Storage
- **Supabase Storage**: Organized in separate buckets
  - `avatars`: User profile pictures
  - `products`: Product images
  - `events`: Event photos
- **Image Optimization**: Consider implementing image compression
- **Access Control**: Implement proper storage policies

## 🐛 Common Issues & Troubleshooting

### Development Issues
1. **Supabase Connection**: Verify environment variables are set correctly
2. **CORS Issues**: Check Supabase project settings for allowed origins
3. **Build Errors**: Clear node_modules and reinstall dependencies
4. **TypeScript Errors**: Check import paths and type definitions

### Mobile Development Issues
1. **Capacitor Sync**: Run `ionic cap sync` after installing new plugins
2. **iOS Build**: Ensure proper code signing and provisioning profiles
3. **Android Build**: Check Android SDK and build tools versions
4. **Push Notifications**: Verify OneSignal configuration and certificates

### Database Issues
1. **RLS Policies**: Ensure policies allow necessary operations
2. **Triggers**: Check database triggers are functioning correctly
3. **Constraints**: Verify data meets constraint requirements
4. **Performance**: Monitor and optimize slow queries

## 📚 Additional Resources

### Documentation
- [Vue 3 Documentation](https://vuejs.org/)
- [Ionic Framework](https://ionicframework.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Capacitor Documentation](https://capacitorjs.com/docs)
- [OneSignal Documentation](https://documentation.onesignal.com/)

### Development Tools
- [Vue DevTools](https://devtools.vuejs.org/)
- [Ionic DevApp](https://ionicframework.com/docs/appflow/devapp)
- [Supabase Dashboard](https://app.supabase.com/)

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes following the coding guidelines
4. Test thoroughly on both web and mobile
5. Submit a pull request with detailed description

### Code Review Process
- Ensure TypeScript compilation passes
- Test on both iOS and Android devices
- Verify database changes don't break existing functionality
- Check for proper error handling and user feedback

## 📞 Support & Contact

For technical questions or issues:
1. Review Supabase logs for backend issues
2. Test on multiple devices and browsers
3. Document steps to reproduce any bugs