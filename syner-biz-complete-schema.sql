/*
  # Complete Syner-Biz Database Schema Export
  # This script recreates the entire database structure from the syner-biz project
  # for importing into the new Waste to Gold project

  Generated on: 2025-06-29
  Source: syner-biz project migrations

  IMPORTANT: This script will drop and recreate conflicting tables to match syner-biz exactly.
  The following Waste to Gold specific tables will be preserved:
  - material_requests
  - material_request_responses
  - districts
*/

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS http;

-- =============================================
-- DROP CONFLICTING TABLES (PRESERVE WASTE TO GOLD SPECIFIC TABLES)
-- =============================================

-- Drop tables that conflict with syner-biz schema (in dependency order)
DROP TABLE IF EXISTS user_liked_products CASCADE;
DROP TABLE IF EXISTS user_liked_shops CASCADE;
DROP TABLE IF EXISTS user_liked_events CASCADE;
DROP TABLE IF EXISTS user_liked_branches CASCADE;
DROP TABLE IF EXISTS user_liked_users CASCADE;
DROP TABLE IF EXISTS shop_ratings CASCADE;
DROP TABLE IF EXISTS product_photos CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS shops CASCADE;
DROP TABLE IF EXISTS event_photos CASCADE;
DROP TABLE IF EXISTS event_applications CASCADE;
DROP TABLE IF EXISTS events CASCADE;
DROP TABLE IF EXISTS branch_activities CASCADE;
DROP TABLE IF EXISTS branch_member_applications CASCADE;
DROP TABLE IF EXISTS branch_members CASCADE;
DROP TABLE IF EXISTS branches CASCADE;
DROP TABLE IF EXISTS branch_categories CASCADE;
DROP TABLE IF EXISTS branch_sources CASCADE;
DROP TABLE IF EXISTS organization_members CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;
DROP TABLE IF EXISTS organization_categories CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS chat_notification_queue CASCADE;
DROP TABLE IF EXISTS user_interaction_logs CASCADE;
DROP TABLE IF EXISTS user_feedback CASCADE;
DROP TABLE IF EXISTS bonus_records CASCADE;
DROP TABLE IF EXISTS cloudflare_deletion_logs CASCADE;
DROP TABLE IF EXISTS cloudflare_config CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop views
DROP VIEW IF EXISTS user_login_info CASCADE;

-- Create update_updated_at function
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- CORE TABLES
-- =============================================

-- Users Table (Exact syner-biz schema)
CREATE TABLE users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  username text UNIQUE,
  full_name text,
  email text UNIQUE,
  phone text,
  role text CHECK (role IN ('free', 'merchant', 'president')),
  referrer_id uuid REFERENCES users(id),
  industry text,
  company_name text,
  referral_code text UNIQUE,
  avatar_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Product Categories Table (Missing from migrations but used in Vue components)
CREATE TABLE product_categories (
  id serial PRIMARY KEY,
  title text NOT NULL UNIQUE,
  description text,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT true,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Products Table (Exact syner-biz schema from latest migration)
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id uuid REFERENCES shops(id),
  title text NOT NULL,
  description text,
  price numeric NOT NULL CHECK (price >= 0),
  is_in_stock boolean DEFAULT true,
  profit_sharing_rate numeric NOT NULL CHECK (profit_sharing_rate >= 0 AND profit_sharing_rate <= 100),
  cover_image text,
  status text DEFAULT 'active' CHECK (status IN ('active', 'trashed')),
  created_by text,
  display_order integer DEFAULT 0,
  category_id integer REFERENCES product_categories(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Product Photos Table (Exact syner-biz schema)
CREATE TABLE product_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  photo_url text NOT NULL,
  caption text,
  "order" integer NOT NULL DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Shops Table (Exact syner-biz schema with metrics)
CREATE TABLE shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  is_featured boolean DEFAULT false,
  notification_emails text[],
  notification_group text,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  product_count integer DEFAULT 0,
  like_count integer DEFAULT 0,
  rating numeric DEFAULT 0,
  rating_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Events Table (Enhanced with datetime support)
CREATE TABLE IF NOT EXISTS events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  title text NOT NULL,
  description text NOT NULL,
  date date,
  start_time time without time zone,
  end_time time without time zone,
  start_datetime timestamptz,
  end_datetime timestamptz,
  address text NOT NULL,
  banner_photo text,
  max_participants integer,
  like_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Event Applications Table
CREATE TABLE IF NOT EXISTS event_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id),
  full_name text NOT NULL,
  email text NOT NULL,
  phone text NOT NULL,
  gender text,
  qr_code text NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'attended')),
  guest_access_token uuid DEFAULT gen_random_uuid(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Event Photos Table
CREATE TABLE IF NOT EXISTS event_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  photo_url text NOT NULL,
  caption text,
  "order" integer NOT NULL DEFAULT 0,
  photo_type text NOT NULL CHECK (photo_type IN ('creation', 'post_event')) DEFAULT 'creation',
  uploaded_by uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Bonus Records Table
CREATE TABLE IF NOT EXISTS bonus_records (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  amount numeric NOT NULL,
  source text NOT NULL,
  transaction_date timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- BRANCH SYSTEM TABLES
-- =============================================

-- Branch Categories Table
CREATE TABLE IF NOT EXISTS branch_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT false,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Branch Sources Table
CREATE TABLE IF NOT EXISTS branch_sources (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT false,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Branches Table
CREATE TABLE IF NOT EXISTS branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  introduction text,
  philosophy text,
  is_featured boolean DEFAULT false,
  category_id uuid REFERENCES branch_categories(id),
  source_id uuid REFERENCES branch_sources(id),
  district text,
  member_count integer DEFAULT 0,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Branch Members Table
CREATE TABLE IF NOT EXISTS branch_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id),
  joined_at timestamptz DEFAULT now(),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'rejected')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- Branch Member Applications Table
CREATE TABLE IF NOT EXISTS branch_member_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id),
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  message text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- Branch Activities Table
CREATE TABLE IF NOT EXISTS branch_activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  start_date date NOT NULL,
  end_date date,
  is_recent boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =============================================
-- ORGANIZATION SYSTEM TABLES
-- =============================================

-- Organization Categories Table
CREATE TABLE IF NOT EXISTS organization_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT false,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Organizations Table
CREATE TABLE IF NOT EXISTS organizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  introduction text,
  philosophy text,
  is_featured boolean DEFAULT false,
  category_id uuid REFERENCES organization_categories(id),
  district text,
  member_count integer DEFAULT 0,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Organization Members Table
CREATE TABLE IF NOT EXISTS organization_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id),
  joined_at timestamptz DEFAULT now(),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, user_id)
);

-- =============================================
-- CHAT SYSTEM TABLES
-- =============================================

-- Conversations Table
CREATE TABLE IF NOT EXISTS conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  participant_1_id uuid NOT NULL REFERENCES auth.users(id),
  participant_2_id uuid NOT NULL REFERENCES auth.users(id),
  last_message_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  UNIQUE(participant_1_id, participant_2_id)
);

-- Messages Table
CREATE TABLE IF NOT EXISTS messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES auth.users(id),
  content text NOT NULL,
  message_type text DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'voice', 'file')),
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Chat Notification Queue Table
CREATE TABLE IF NOT EXISTS chat_notification_queue (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id uuid NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  conversation_id uuid NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES auth.users(id),
  recipient_id uuid NOT NULL REFERENCES auth.users(id),
  content text NOT NULL,
  message_type text NOT NULL,
  processed boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- LIKE/RATING SYSTEM TABLES
-- =============================================

-- User Liked Shops Table
CREATE TABLE IF NOT EXISTS user_liked_shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  shop_id uuid NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, shop_id)
);

-- User Liked Products Table
CREATE TABLE IF NOT EXISTS user_liked_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, product_id)
);

-- User Liked Events Table
CREATE TABLE IF NOT EXISTS user_liked_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, event_id)
);

-- User Liked Branches Table
CREATE TABLE IF NOT EXISTS user_liked_branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, branch_id)
);

-- User Liked Users Table
CREATE TABLE IF NOT EXISTS user_liked_users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  liked_user_id uuid NOT NULL REFERENCES auth.users(id),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, liked_user_id)
);

-- Shop Ratings Table
CREATE TABLE IF NOT EXISTS shop_ratings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  shop_id uuid NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, shop_id)
);

-- =============================================
-- FEEDBACK AND LOGGING TABLES
-- =============================================

-- User Feedback Table
CREATE TABLE IF NOT EXISTS user_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  category text NOT NULL CHECK (category IN ('bug', 'feature', 'improvement', 'ui', 'performance', 'other')),
  subject text NOT NULL,
  description text NOT NULL,
  contact_email text,
  is_anonymous boolean DEFAULT false,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'resolved', 'closed')),
  admin_notes text,
  admin_response text,
  responded_by uuid REFERENCES auth.users(id),
  responded_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User Interaction Logs Table
CREATE TABLE IF NOT EXISTS user_interaction_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  target_user_id uuid NOT NULL REFERENCES auth.users(id),
  action_type text NOT NULL,
  notes text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT valid_action_type CHECK (action_type IN ('like', 'unlike', 'message', 'view'))
);

-- =============================================
-- CLOUDFLARE INTEGRATION TABLES
-- =============================================

-- Cloudflare Config Table
CREATE TABLE IF NOT EXISTS cloudflare_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id text NOT NULL,
  api_key text NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Cloudflare Deletion Logs Table
CREATE TABLE IF NOT EXISTS cloudflare_deletion_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  record_id uuid NOT NULL,
  image_url text,
  image_id text,
  success boolean NOT NULL,
  response jsonb,
  error text,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users indexes
CREATE INDEX users_username_idx ON users(username);
CREATE INDEX users_email_idx ON users(email);
CREATE INDEX users_referrer_id_idx ON users(referrer_id);
CREATE INDEX users_referral_code_idx ON users(referral_code);

-- Product Categories indexes
CREATE INDEX product_categories_sort_order_idx ON product_categories(sort_order);
CREATE INDEX product_categories_is_approved_idx ON product_categories(is_approved);

-- Products indexes
CREATE INDEX products_shop_id_idx ON products(shop_id);
CREATE INDEX products_created_by_idx ON products(created_by);
CREATE INDEX products_display_order_idx ON products(shop_id, display_order);
CREATE INDEX products_status_idx ON products(status);
CREATE INDEX products_category_id_idx ON products(category_id);

-- Product Photos indexes
CREATE INDEX product_photos_product_id_idx ON product_photos(product_id);
CREATE INDEX product_photos_order_idx ON product_photos(product_id, "order");

-- Shops indexes
CREATE INDEX IF NOT EXISTS shops_owner_id_idx ON shops(owner_id);
CREATE INDEX IF NOT EXISTS shops_is_featured_idx ON shops(is_featured);

-- Events indexes
CREATE INDEX IF NOT EXISTS events_user_id_idx ON events(user_id);
CREATE INDEX IF NOT EXISTS events_date_idx ON events(date);
CREATE INDEX IF NOT EXISTS events_start_datetime_idx ON events(start_datetime);

-- Event Applications indexes
CREATE INDEX IF NOT EXISTS event_applications_event_id_idx ON event_applications(event_id);
CREATE INDEX IF NOT EXISTS event_applications_user_id_idx ON event_applications(user_id);
CREATE INDEX IF NOT EXISTS event_applications_status_idx ON event_applications(status);

-- Event Photos indexes
CREATE INDEX IF NOT EXISTS event_photos_event_id_idx ON event_photos(event_id);
CREATE INDEX IF NOT EXISTS event_photos_uploaded_by_idx ON event_photos(uploaded_by);

-- Bonus Records indexes
CREATE INDEX IF NOT EXISTS bonus_records_user_id_idx ON bonus_records(user_id);

-- Branch system indexes
CREATE INDEX IF NOT EXISTS branches_owner_id_idx ON branches(owner_id);
CREATE INDEX IF NOT EXISTS branches_category_id_idx ON branches(category_id);
CREATE INDEX IF NOT EXISTS branches_source_id_idx ON branches(source_id);
CREATE INDEX IF NOT EXISTS branches_is_featured_idx ON branches(is_featured);
CREATE INDEX IF NOT EXISTS branches_district_idx ON branches(district);

CREATE INDEX IF NOT EXISTS branch_members_branch_id_idx ON branch_members(branch_id);
CREATE INDEX IF NOT EXISTS branch_members_user_id_idx ON branch_members(user_id);
CREATE INDEX IF NOT EXISTS branch_members_status_idx ON branch_members(status);

CREATE INDEX IF NOT EXISTS branch_member_applications_branch_id_idx ON branch_member_applications(branch_id);
CREATE INDEX IF NOT EXISTS branch_member_applications_user_id_idx ON branch_member_applications(user_id);
CREATE INDEX IF NOT EXISTS branch_member_applications_status_idx ON branch_member_applications(status);

CREATE INDEX IF NOT EXISTS branch_activities_branch_id_idx ON branch_activities(branch_id);
CREATE INDEX IF NOT EXISTS branch_activities_is_recent_idx ON branch_activities(is_recent);

-- Organization system indexes
CREATE INDEX IF NOT EXISTS organizations_owner_id_idx ON organizations(owner_id);
CREATE INDEX IF NOT EXISTS organizations_category_id_idx ON organizations(category_id);
CREATE INDEX IF NOT EXISTS organizations_is_featured_idx ON organizations(is_featured);

CREATE INDEX IF NOT EXISTS organization_members_organization_id_idx ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS organization_members_user_id_idx ON organization_members(user_id);

-- Chat system indexes
CREATE INDEX IF NOT EXISTS conversations_participant_1_id_idx ON conversations(participant_1_id);
CREATE INDEX IF NOT EXISTS conversations_participant_2_id_idx ON conversations(participant_2_id);
CREATE INDEX IF NOT EXISTS conversations_last_message_at_idx ON conversations(last_message_at);

CREATE INDEX IF NOT EXISTS messages_conversation_id_idx ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS messages_sender_id_idx ON messages(sender_id);
CREATE INDEX IF NOT EXISTS messages_created_at_idx ON messages(created_at);

CREATE INDEX IF NOT EXISTS chat_notification_queue_processed_idx ON chat_notification_queue(processed, created_at);
CREATE INDEX IF NOT EXISTS chat_notification_queue_message_id_idx ON chat_notification_queue(message_id);

-- Like/Rating system indexes
CREATE INDEX IF NOT EXISTS user_liked_shops_user_id_idx ON user_liked_shops(user_id);
CREATE INDEX IF NOT EXISTS user_liked_shops_shop_id_idx ON user_liked_shops(shop_id);

CREATE INDEX IF NOT EXISTS user_liked_products_user_id_idx ON user_liked_products(user_id);
CREATE INDEX IF NOT EXISTS user_liked_products_product_id_idx ON user_liked_products(product_id);

CREATE INDEX IF NOT EXISTS user_liked_events_user_id_idx ON user_liked_events(user_id);
CREATE INDEX IF NOT EXISTS user_liked_events_event_id_idx ON user_liked_events(event_id);

CREATE INDEX IF NOT EXISTS user_liked_branches_user_id_idx ON user_liked_branches(user_id);
CREATE INDEX IF NOT EXISTS user_liked_branches_branch_id_idx ON user_liked_branches(branch_id);

CREATE INDEX IF NOT EXISTS user_liked_users_user_id_idx ON user_liked_users(user_id);
CREATE INDEX IF NOT EXISTS user_liked_users_liked_user_id_idx ON user_liked_users(liked_user_id);

CREATE INDEX IF NOT EXISTS shop_ratings_user_id_idx ON shop_ratings(user_id);
CREATE INDEX IF NOT EXISTS shop_ratings_shop_id_idx ON shop_ratings(shop_id);

-- Feedback and logging indexes
CREATE INDEX IF NOT EXISTS user_feedback_user_id_idx ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS user_feedback_status_idx ON user_feedback(status);
CREATE INDEX IF NOT EXISTS user_feedback_category_idx ON user_feedback(category);

CREATE INDEX IF NOT EXISTS user_interaction_logs_user_id_idx ON user_interaction_logs(user_id);
CREATE INDEX IF NOT EXISTS user_interaction_logs_target_user_id_idx ON user_interaction_logs(target_user_id);
CREATE INDEX IF NOT EXISTS user_interaction_logs_action_type_idx ON user_interaction_logs(action_type);

-- Cloudflare indexes
CREATE INDEX IF NOT EXISTS cloudflare_deletion_logs_created_at_idx ON cloudflare_deletion_logs(created_at);

-- =============================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =============================================

CREATE TRIGGER users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER product_categories_updated_at
  BEFORE UPDATE ON product_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER shops_updated_at
  BEFORE UPDATE ON shops
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER events_updated_at
  BEFORE UPDATE ON events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER event_applications_updated_at
  BEFORE UPDATE ON event_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER event_photos_updated_at
  BEFORE UPDATE ON event_photos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branch_categories_updated_at
  BEFORE UPDATE ON branch_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branch_sources_updated_at
  BEFORE UPDATE ON branch_sources
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branches_updated_at
  BEFORE UPDATE ON branches
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branch_members_updated_at
  BEFORE UPDATE ON branch_members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branch_member_applications_updated_at
  BEFORE UPDATE ON branch_member_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER branch_activities_updated_at
  BEFORE UPDATE ON branch_activities
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER organization_categories_updated_at
  BEFORE UPDATE ON organization_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER organization_members_updated_at
  BEFORE UPDATE ON organization_members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER user_liked_users_updated_at
  BEFORE UPDATE ON user_liked_users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER shop_ratings_updated_at
  BEFORE UPDATE ON shop_ratings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER user_feedback_updated_at
  BEFORE UPDATE ON user_feedback
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- =============================================
-- SPECIALIZED FUNCTIONS AND TRIGGERS
-- =============================================

-- Generate referral code function
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS text AS $$
DECLARE
  code text;
  exists boolean;
BEGIN
  LOOP
    code := upper(substring(md5(random()::text) from 1 for 8));
    SELECT EXISTS(SELECT 1 FROM users WHERE referral_code = code) INTO exists;
    IF NOT exists THEN
      EXIT;
    END IF;
  END LOOP;
  RETURN code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Handle new user function (Complete version matching syner-biz)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_role text;
  new_industry text;
  new_company_name text;
  new_username text;
  new_full_name text;
  new_phone text;
  referrer_id uuid;
  new_referral_code text;
BEGIN
  -- Extract all fields with proper null handling
  new_username := NEW.raw_user_meta_data->>'username';
  new_full_name := NEW.raw_user_meta_data->>'full_name';
  new_phone := NEW.raw_user_meta_data->>'phone';
  new_role := COALESCE(NEW.raw_user_meta_data->>'role', 'free');
  new_industry := NULLIF(TRIM(NEW.raw_user_meta_data->>'industry'), '');
  new_company_name := NULLIF(TRIM(NEW.raw_user_meta_data->>'company_name'), '');

  -- Validate referral code and get referrer_id
  IF NEW.raw_user_meta_data->>'referral_code' IS NOT NULL THEN
    SELECT id INTO referrer_id
    FROM public.users
    WHERE LOWER(referral_code) = LOWER(NEW.raw_user_meta_data->>'referral_code');
  END IF;

  -- Generate unique referral code
  new_referral_code := generate_referral_code();

  -- Validate required fields
  IF new_username IS NULL OR TRIM(new_username) = '' THEN
    RAISE EXCEPTION 'Username is required';
  END IF;

  IF new_full_name IS NULL OR TRIM(new_full_name) = '' THEN
    RAISE EXCEPTION 'Full name is required';
  END IF;

  IF new_phone IS NULL OR TRIM(new_phone) = '' THEN
    RAISE EXCEPTION 'Phone number is required';
  END IF;

  -- Validate role
  IF new_role NOT IN ('free', 'merchant', 'president') THEN
    new_role := 'free';
  END IF;

  -- Insert new user with validated fields (matching actual table structure)
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name,
    referral_code,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    new_username,
    new_full_name,
    NEW.email,
    new_phone,
    new_role,
    referrer_id,
    new_industry,
    new_company_name,
    new_referral_code,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  );

  RETURN NEW;
EXCEPTION WHEN OTHERS THEN
  -- Log the error details
  RAISE LOG 'Error in handle_new_user: %', SQLERRM;
  -- Re-raise the error with a user-friendly message
  RAISE EXCEPTION 'Failed to create user profile: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update branch member count function
CREATE OR REPLACE FUNCTION update_branch_member_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE branches
    SET member_count = member_count + 1
    WHERE id = NEW.branch_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE branches
    SET member_count = member_count - 1
    WHERE id = OLD.branch_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Handle approved application function
CREATE OR REPLACE FUNCTION handle_approved_application()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'approved' AND OLD.status = 'pending' THEN
    INSERT INTO branch_members (branch_id, user_id)
    VALUES (NEW.branch_id, NEW.user_id)
    ON CONFLICT (branch_id, user_id) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Queue chat notification function
CREATE OR REPLACE FUNCTION queue_chat_notification()
RETURNS TRIGGER AS $$
DECLARE
  conversation_record RECORD;
  recipient_id uuid;
BEGIN
  IF TG_OP = 'INSERT' THEN
    SELECT * INTO conversation_record
    FROM conversations
    WHERE id = NEW.conversation_id;

    IF NOT FOUND THEN
      RAISE LOG 'Conversation not found for message %', NEW.id;
      RETURN NEW;
    END IF;

    IF conversation_record.participant_1_id = NEW.sender_id THEN
      recipient_id := conversation_record.participant_2_id;
    ELSE
      recipient_id := conversation_record.participant_1_id;
    END IF;

    INSERT INTO chat_notification_queue (
      message_id,
      conversation_id,
      sender_id,
      recipient_id,
      content,
      message_type
    ) VALUES (
      NEW.id,
      NEW.conversation_id,
      NEW.sender_id,
      recipient_id,
      NEW.content,
      NEW.message_type
    );

    RAISE LOG 'Chat notification queued for message %', NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get Cloudflare credentials function
CREATE OR REPLACE FUNCTION get_cloudflare_credentials()
RETURNS TABLE (account_id text, api_key text) AS $$
BEGIN
  RETURN QUERY
  SELECT cf.account_id, cf.api_key
  FROM cloudflare_config cf
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGER ASSIGNMENTS
-- =============================================

-- Auth user creation trigger (IMPORTANT: Must be on auth.users, not public.users)
DROP TRIGGER IF EXISTS on_auth_user_created ON users;  -- Remove any incorrect trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Branch member count triggers
CREATE TRIGGER update_branch_member_count_trigger
  AFTER INSERT OR DELETE ON branch_members
  FOR EACH ROW
  EXECUTE FUNCTION update_branch_member_count();

-- Branch application approval trigger
CREATE TRIGGER handle_approved_application_trigger
  AFTER UPDATE ON branch_member_applications
  FOR EACH ROW
  EXECUTE FUNCTION handle_approved_application();

-- Chat notification trigger
DROP TRIGGER IF EXISTS queue_chat_notification_trigger ON messages;
CREATE TRIGGER queue_chat_notification_trigger
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION queue_chat_notification();

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE bonus_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_member_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_notification_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_liked_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_liked_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_liked_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_liked_branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_liked_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_interaction_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE cloudflare_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE cloudflare_deletion_logs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- ESSENTIAL RLS POLICIES
-- =============================================

-- Users policies
CREATE POLICY "Users can read their own profile"
  ON users FOR SELECT TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Allow referral code lookups"
  ON users FOR SELECT TO public
  USING (true);

-- Product Categories policies
CREATE POLICY "Anyone can view approved product categories"
  ON product_categories FOR SELECT TO public
  USING (is_approved = true);

CREATE POLICY "Authenticated users can create product categories"
  ON product_categories FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = created_by);

-- Products policies
CREATE POLICY "Anyone can view active products"
  ON products FOR SELECT TO public
  USING (status = 'active');

CREATE POLICY "Shop owners can manage their products"
  ON products FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Shops policies
CREATE POLICY "Anyone can view shops"
  ON shops FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Users can create their own shop"
  ON shops FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own shop"
  ON shops FOR UPDATE TO authenticated
  USING (auth.uid() = owner_id);

-- Events policies
CREATE POLICY "Anyone can view events"
  ON events FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Users can manage their own events"
  ON events FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Chat policies
CREATE POLICY "Users can view their conversations"
  ON conversations FOR SELECT TO authenticated
  USING (auth.uid() = participant_1_id OR auth.uid() = participant_2_id);

CREATE POLICY "Users can create conversations"
  ON conversations FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = participant_1_id OR auth.uid() = participant_2_id);

CREATE POLICY "Users can view messages in their conversations"
  ON messages FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND (conversations.participant_1_id = auth.uid() OR conversations.participant_2_id = auth.uid())
    )
  );

CREATE POLICY "Users can send messages in their conversations"
  ON messages FOR INSERT TO authenticated
  WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND (conversations.participant_1_id = auth.uid() OR conversations.participant_2_id = auth.uid())
    )
  );

-- Like system policies
CREATE POLICY "Users can manage their own liked shops"
  ON user_liked_shops FOR ALL TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own liked products"
  ON user_liked_products FOR ALL TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own liked events"
  ON user_liked_events FOR ALL TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own liked users"
  ON user_liked_users FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- Branch system policies
CREATE POLICY "Anyone can view approved branch categories"
  ON branch_categories FOR SELECT TO authenticated
  USING (is_approved = true);

CREATE POLICY "Anyone can view branches"
  ON branches FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Users can create their own branch"
  ON branches FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own branch"
  ON branches FOR UPDATE TO authenticated
  USING (auth.uid() = owner_id);

-- System-only policies
CREATE POLICY "System only access to notification queue"
  ON chat_notification_queue FOR ALL TO authenticated
  USING (false);

CREATE POLICY "Only superuser can manage cloudflare config"
  ON cloudflare_config FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.is_super_admin = true
    )
  );

-- =============================================
-- STORAGE BUCKETS
-- =============================================

-- Create storage bucket for products
INSERT INTO storage.buckets (id, name, public)
VALUES ('products', 'products', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for products bucket
CREATE POLICY "Allow authenticated uploads to products bucket"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'products');

CREATE POLICY "Allow authenticated updates to products bucket"
ON storage.objects FOR UPDATE TO authenticated
USING (bucket_id = 'products' AND auth.uid() = owner);

CREATE POLICY "Allow authenticated deletes from products bucket"
ON storage.objects FOR DELETE TO authenticated
USING (bucket_id = 'products' AND auth.uid() = owner);

CREATE POLICY "Allow public read access to products bucket"
ON storage.objects FOR SELECT TO public
USING (bucket_id = 'products');

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert predefined product categories
INSERT INTO product_categories (title, description, sort_order, is_predefined, is_approved)
VALUES
  ('建築材料', '建築和裝修材料', 1, true, true),
  ('電子設備', '電子產品和設備', 2, true, true),
  ('家具', '家具和家居用品', 3, true, true),
  ('工具', '工具和機械設備', 4, true, true),
  ('辦公用品', '辦公室用品和設備', 5, true, true),
  ('其他', '其他類型的產品', 999, true, true)
ON CONFLICT (title) DO NOTHING;

-- Insert predefined branch categories
INSERT INTO branch_categories (title, description, sort_order, is_predefined, is_approved)
VALUES
  ('商務類', '商務相關的分會', 1, true, true),
  ('社交類', '社交相關的分會', 2, true, true),
  ('興趣類', '興趣相關的分會', 3, true, true),
  ('專業/產業類', '專業或產業相關的分會', 4, true, true),
  ('銷售類', '銷售相關的分會', 5, true, true),
  ('其他', '其他類型的分會', 999, true, true)
ON CONFLICT (title) DO NOTHING;

-- Insert predefined branch sources
INSERT INTO branch_sources (name, sort_order, is_predefined, is_approved)
VALUES
  ('BNI', 1, true, true),
  ('提多商業聯盟', 2, true, true),
  ('商龍會', 3, true, true),
  ('香港中小企協會', 4, true, true),
  ('工聯會', 5, true, true),
  ('其他', 999, true, true)
ON CONFLICT (name) DO NOTHING;

-- Insert predefined organization categories
INSERT INTO organization_categories (name, description, sort_order, is_predefined, is_approved)
VALUES
  ('商會', '商業相關的組織', 1, true, true),
  ('產業協會', '特定產業的專業協會', 2, true, true),
  ('非政府組織（NGO）', '非營利的非政府組織', 3, true, true),
  ('慈善機構', '慈善和公益組織', 4, true, true),
  ('專業團體', '專業人士的組織', 5, true, true),
  ('校友會', '學校校友組織', 6, true, true),
  ('俱樂部和社團', '興趣和社交組織', 7, true, true),
  ('其他', '其他類型的組織', 999, true, true)
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- VIEWS
-- =============================================

-- Create secure view for login lookups
CREATE OR REPLACE VIEW user_login_info AS
SELECT id, email, username
FROM users;

-- Grant access to the view
GRANT SELECT ON user_login_info TO anon, authenticated;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION queue_chat_notification() IS
'Automatically queues push notifications when new chat messages are inserted.
Notifications are processed by a separate service or edge function.';

COMMENT ON TRIGGER queue_chat_notification_trigger ON messages IS
'Queues push notifications for new chat messages.';

COMMENT ON TABLE chat_notification_queue IS
'Queue table for chat notifications. Processed by edge functions or background services.';

COMMENT ON FUNCTION generate_referral_code() IS
'Generates a unique 8-character referral code for users.';

COMMENT ON FUNCTION handle_new_user() IS
'Automatically creates a user profile when a new auth user is created.';

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
  RAISE NOTICE 'Syner-Biz database schema import completed successfully!';
  RAISE NOTICE 'Conflicting tables dropped and recreated with exact syner-biz schema';
  RAISE NOTICE 'Added missing product_categories table referenced in Vue components';
  RAISE NOTICE 'Preserved Waste to Gold specific tables: material_requests, material_request_responses, districts';
  RAISE NOTICE 'Tables: %, Functions: %, Triggers: %, Policies: %',
    (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public'),
    (SELECT count(*) FROM information_schema.routines WHERE routine_schema = 'public'),
    (SELECT count(*) FROM information_schema.triggers WHERE trigger_schema = 'public'),
    (SELECT count(*) FROM pg_policies WHERE schemaname = 'public');
END $$;
